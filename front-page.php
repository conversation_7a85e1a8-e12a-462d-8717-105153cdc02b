<?php
/**
 * The front page template file
 *
 * This is the template that displays the home page.
 * WordPress will use this template for the front page when it exists.
 *
 * @package Krystelis_Custom
 */

get_header(); ?>

<div id="content" class="site-content">
    <?php
    // Hero Section
    get_template_part('template-parts/hero');

    // Services Section
    get_template_part('template-parts/services');

    // CTA Section
    get_template_part('template-parts/cta-section');
    ?>
</div>

<style>
/* Ensure smooth scrolling and prevent layout issues */
 html {
    scroll-behavior: smooth;
}
/*
body {
    overflow-x: hidden;
} */

/* Prevent any container from causing horizontal scroll */
/* * {
    box-sizing: border-box;
} */

/* Base styles */
.site-content {
    margin: 0;
    padding: 0;
    margin-top: -171px;
    max-width: 100vw;
    overflow-x: hidden;
}

.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;
}

/* 1. Ultra-wide Desktop */
@media (min-width: 1921px) and (max-width: 2560px) {
    .container {
        max-width: 2000px;
    }

    .why-how-boxes {
        max-width: 1800px;
        margin: 10.5rem auto;
    }

    .box-content h3 {
        font-size: 2.5rem;
    }

    .box-content p {
        font-size: 1.4rem;
    }

    .who-content h2 {
        font-size: 3.5rem;
    }

    .who-content p {
        font-size: 1.6rem;
    }
}

/* 2. Large Desktop */
@media (min-width: 1441px) and (max-width: 1920px) {
    .container {
        max-width: 1600px;
    }

    .why-how-boxes {
        max-width: 1400px;
        margin: 10.5rem auto;
    }

    .box-content h3 {
        font-size: 2.2rem;
    }

    .box-content p {
        font-size: 1.3rem;
    }

    .who-content h2 {
        font-size: 3.2rem;
    }

    .who-content p {
        font-size: 1.4rem;
    }
}

/* 3. Standard Desktop */
@media (min-width: 1281px) and (max-width: 1440px) {
    .container {
        max-width: 1200px;
    }

    .why-how-boxes {
        max-width: 1100px;
        margin: 10.5rem auto;
    }

    .box-content h3 {
        font-size: 2rem;
    }

    .box-content p {
        font-size: 1.2rem;
    }

    .who-content h2 {
        font-size: 3rem;
    }

    .who-content p {
        font-size: 1.3rem;
    }
}

/* 4. Small Desktop / Large Laptop */
@media (min-width: 1025px) and (max-width: 1280px) {
    .container {
        max-width: 1000px;
    }

    .why-how-boxes {
        max-width: 900px;
        margin: 8rem auto;
    }

    .box-content h3 {
        font-size: 1.8rem;
    }

    .box-content p {
        font-size: 1.1rem;
    }

    .who-content h2 {
        font-size: 2.8rem;
    }

    .who-content p {
        font-size: 1.2rem;
    }
}

/* 5. Tablet Landscape */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 900px;
    }

    .why-how-boxes {
        max-width: 800px;
        margin: 6rem auto;
    }

    .box-content h3 {
        font-size: 1.6rem;
    }

    .box-content p {
        font-size: 1rem;
    }

    .who-content h2 {
        font-size: 2.5rem;
    }

    .who-content p {
        font-size: 1.1rem;
    }

    .who-content {
        margin-right: 30px;
        margin-left: 15px;
    }
}

/* 6. Tablet Portrait / Phablet */
@media (min-width: 601px) and (max-width: 768px) {
    .container {
        max-width: 100%;
        padding: 0 10px;
    }

    .why-how-boxes {
        flex-direction: column;
        gap: 1rem;
        margin: 4rem auto;
    }

    .why-box,
    .how-box {
        padding: 1.5rem;
        margin: 0.5rem 0;
    }

    .box-content h3 {
        font-size: 1.4rem;
    }

    .box-content p {
        font-size: 0.9rem;
    }

    .who-content {
        margin: 0 10px;
        text-align: center;
    }

    .who-content h2 {
        font-size: 2.2rem;
        margin-left: 0;
    }

    .who-content p {
        font-size: 1rem;
    }
}

/* 7. Large Mobile */
@media (min-width: 481px) and (max-width: 600px) {
    .container {
        padding: 0 8px;
    }

    .why-how-boxes {
        margin: 3rem auto;
    }

    .box-content h3 {
        font-size: 1.3rem;
    }

    .box-content p {
        font-size: 0.85rem;
    }

    .who-content h2 {
        font-size: 2rem;
    }

    .who-content p {
        font-size: 0.9rem;
    }
}

/* 8. Medium Mobile */
@media (min-width: 376px) and (max-width: 480px) {
    .container {
        padding: 0 5px;
    }

    .why-how-boxes {
        margin: 2.5rem auto;
    }

    .box-content h3 {
        font-size: 1.2rem;
    }

    .box-content p {
        font-size: 0.8rem;
    }

    .who-content h2 {
        font-size: 1.8rem;
    }

    .who-content p {
        font-size: 0.85rem;
    }
}

/* 9. Small Mobile */
@media (min-width: 321px) and (max-width: 375px) {
    .container {
        padding: 0 4px;
    }

    .why-how-boxes {
        margin: 2rem auto;
    }

    .box-content h3 {
        font-size: 1.1rem;
    }

    .box-content p {
        font-size: 0.75rem;
    }

    .who-content h2 {
        font-size: 1.6rem;
    }

    .who-content p {
        font-size: 0.8rem;
    }
}

/* 10. Extra Small Mobile */
@media (max-width: 320px) {
    .container {
        padding: 0 3px;
    }

    .why-how-boxes {
        margin: 1.5rem auto;
    }

    .box-content h3 {
        font-size: 1rem;
    }

    .box-content p {
        font-size: 0.7rem;
    }

    .who-content h2 {
        font-size: 1.4rem;
    }

    .who-content p {
        font-size: 0.75rem;
    }
}

/* Common responsive styles */
.why-box,
.how-box {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.why-box:hover,
.how-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(74, 144, 226, 0.3);
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Ensure text remains readable */
p, h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Ensure proper spacing between sections */
.services-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

/* Ensure buttons maintain proper spacing */
.btn {
    margin: 0.5rem 0;
    white-space: normal;
    word-wrap: break-word;
}

/* Ensure proper background image scaling */
.why-how-section {
    background-size: cover;
    background-position: center;
}

/* Home page specific styles */
.site-content {
    margin: 0;
    padding: 0;
    margin-top: -171px;
}

/* Ensure full-width layout with minimal margins as per user preferences */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px; /* Proper horizontal padding for content */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 2px; /* Maintain minimal margins on mobile */
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1400px; /* Allow content to stretch more on larger screens */
        padding: 0 2px;
    }
}

/* Ensure sections flow seamlessly */
.hero-section,
.services-section,
.cta-section {
    margin: 0;
}

/* Override any excessive padding that might exist */
.services-section,
.cta-section {
    padding-left: 0;
    padding-right: 0;
}

/* Ensure hero section spans full width */
.hero-section {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
}

/* Make sure content areas utilize full available space */
.services-grid,
.cta-content {
    width: 100%;
}

/* Adjust section spacing for better flow */
.services-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

/* Why/How We Do It Section Styles */
.why-how-section {
    background-color: #f8f9fa;
    position: relative;
    overflow-x: hidden;
    background-image:
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-Orange.png'),
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-blue.png');
    background-position:
        top right,
        bottom left;
    background-repeat: no-repeat, no-repeat;
    background-size: 300px auto, 250px auto;
}

.orange-diagonal-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(135deg, #FF6B35 0%, #FF8A5B 100%);
    clip-path: polygon(0 0, 100% 0, 85% 100%, 0 100%);
}

.medical-icons {
    position: absolute;
    right: 10%;
    top: 50%;
    transform: translateY(-50%);
}

.read-more-top {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.read-more-btn-top {
    display: inline-block;
    background: #FF6B35;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.read-more-btn-top:hover {
    background: #E55A2B;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

/* Why/How boxes styling */
.why-how-boxes {
    display: flex;
    gap: 1rem;
    margin-bottom: 4rem;
    justify-content: center;
    max-width: 1600px;
    margin-top: 10.50rem;
    justify-content: center;
    margin-left: 20px;
    margin-right: 20px;
    /* opacity: 0;
    transform: translateY(100px);
    transition: opacity 2s cubic-bezier(0.23, 1, 0.32, 1), transform 2s cubic-bezier(0.23, 1, 0.32, 1);
}

.why-how-boxes.slide-in {
    opacity: 1;
    transform: translateY(0); */
}

.why-box{
    flex: 1;
    background: linear-gradient(180deg, #41A4FF 2%, #0072DA 100%);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 8rem;
    min-height: 90px;
    box-shadow: 0 8px 30px rgba(74, 144, 226, 0.25);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.how-box {
    flex: 1;
    background: linear-gradient(180deg, #41A4FF 2%, #0072DA 100%);
    border-radius: 25px;
    padding: 2rem;
    color: white;
    display: flex;
    margin-top: 8rem;
    flex-direction: row;
    align-items: center;
    gap: 1.5rem;
    min-height: 90px;
    box-shadow: 0 8px 30px rgba(74, 144, 226, 0.25);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.box-icon {
    flex-shrink: 0;
    margin-bottom: 0;
}

.box-icon img {
    width: 90px;
    height: 90px;
    object-fit: contain;
}

.box-content h3 {
    font-size: 2rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
    color: white;
    letter-spacing: 0.5px;
}

.box-content p {
    font-size: 1.2rem;
    line-height: 1.5;
    margin: 0;
    opacity: 0.95;
}

/* Responsive design for why-how section */
@media (max-width: 768px) {
    .top-section {
        height: 120px;
    }

    .orange-diagonal-bg {
        clip-path: polygon(0 0, 100% 0, 90% 100%, 0 100%);
    }

    .read-more-top {
        left: 20px;
    }

    .read-more-btn-top {
        padding: 10px 20px;
        font-size: 0.8rem;
    }

    .why-how-container {
        padding: 0rem 5px 0rem 5px;;
            }

    .why-how-boxes {
        flex-direction: column;
        gap: 5px;
        margin-top: 1rem;
        max-width: none;
    }

    .why-box,
    .how-box {
        padding: 1.5rem;
        min-height: auto;
        margin-bottom: 5px;
        margin-top: 20px;
    }

    .who-we-are-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .who-content {
    padding-left: 0;
    flex: 1;
    min-width: 300px;
    margin-right: 20px;
    margin-left: 20px;
    }

    .who-content h2 {
        font-size: 2.50rem;
        align-items: center;
    }

   
}

@media (max-width: 768px) {
    .top-section {
        height: 100px;
    }

    .read-more-btn-top {
        padding: 8px 16px;
        font-size: 0.75rem;
    }

    .why-how-container {
        padding: 1.5rem 5px 2rem 5px;
    }

    .box-content h3 {
        font-size: 1.2rem;
    }

    .box-content p {
        font-size: 0.9rem;
    }

    .who-content h2 {
        font-size: 2.50rem;
        align-items: center;
        margin-left: 4rem;
    }

    .who-content p {
        font-size: 1.25rem;
    }
}

/* Responsive Design for All Sections */
/* 320px - 599px (Mobile) */
@media (min-width: 320px) and (max-width: 599px) {
    .site-content {
        margin-top: -120px;
    }

    .container {
        padding: 0 10px;
    }

    .why-how-boxes {
        margin: 2rem auto;
    }

    .box-content h3 {
        font-size: 1.2rem;
    }

    .box-content p {
        font-size: 0.9rem;
    }

    .who-content h2 {
        font-size: 1.8rem;
    }

    .who-content p {
        font-size: 1rem;
    }
}

/* 320px - 767px (Large Mobile) */
@media (min-width: 320px) and (max-width: 767px) {
    .site-content {
        margin-top: -130px;
    }

    .container {
        padding: 0 15px;
    }

    .why-how-boxes {
        margin: 2.5rem auto;
        margin-left: 20px;
        margin-right: 20px;
    }

    .box-content h3 {
        font-size: 1.3rem;
    }

    .box-content p {
        font-size: 1rem;
    }

    .who-content h2 {
        font-size: 2rem;
        margin-right: 35px;
    }

    .who-content p {
        font-size: 1.1rem;
    }

    .team-image-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        padding: 0;
    }

    .team-image {
        width: 100%;
        height: auto;
        margin: 0;
        object-fit: cover;
        margin-right: 15px;
    }
}

/* 768px - 599px (Tablet) */
@media (min-width: 768px) and (max-width: 599px) {
    .site-content {
        margin-top: -140px;
    }

    .container {
        padding: 0 20px;
    }

    .why-how-boxes {
        margin: 3rem auto;
    }

    .box-content h3 {
        font-size: 1.4rem;
    }

    .box-content p {
        font-size: 1.1rem;
    }

    .who-content h2 {
        font-size: 2.2rem;
    }

    .who-content p {
        font-size: 1.2rem;
    }
}

/* 1024px - 599px (Small Desktop) */
@media (min-width: 1024px) and (max-width: 599px) {
    .site-content {
        margin-top: -150px;
    }

    .container {
        padding: 0 25px;
    }

    .why-how-boxes {
        margin: 3.5rem auto;
    }

    .box-content h3 {
        font-size: 1.5rem;
    }

    .box-content p {
        font-size: 1.2rem;
    }

    .who-content h2 {
        font-size: 2.4rem;
    }

    .who-content p {
        font-size: 1.3rem;
    }
}

/* 2560px - 599px (Large Desktop) */
@media (min-width: 2560px) and (max-width: 599px) {
    .site-content {
        margin-top: -160px;
    }

    .container {
        padding: 0 30px;
    }

    .why-how-boxes {
        margin: 4rem auto;
    }

    .box-content h3 {
        font-size: 1.6rem;
    }

    .box-content p {
        font-size: 1.3rem;
    }

    .who-content h2 {
        font-size: 2.6rem;
    }

    .who-content p {
        font-size: 1.4rem;
    }
}

/* 3425px - 599px (Ultra-wide Desktop) */
@media (min-width: 3425px) and (max-width: 599px) {
    .site-content {
        margin-top: -170px;
    }

    .container {
        padding: 0 35px;
    }

    .why-how-boxes {
        margin: 4.5rem auto;
    }

    .box-content h3 {
        font-size: 1.7rem;
    }

    .box-content p {
        font-size: 1.4rem;
    }

    .who-content h2 {
        font-size: 2.8rem;
    }

    .who-content p {
        font-size: 1.5rem;
    }
}

/* Common responsive styles for all screen sizes */
/* Slide-up animation keyframes */
@keyframes slideUpFade {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Initial state before animation */
.why-box,
.how-box {
  opacity: 0;
  transform: translateY(50px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Triggered animation class */
.animate-slide-up {
  animation: slideUpFade 0.8s ease-out forwards;
}

.why-box:hover,
.how-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(74, 144, 226, 0.3);
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Ensure text remains readable */
p, h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Ensure proper spacing between sections */
.services-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

/* Ensure buttons maintain proper spacing */
.btn {
    margin: 0.5rem 0;
    white-space: normal;
    word-wrap: break-word;
}

/* Prevent horizontal scroll */
html, body {
    max-width: 100%;
    overflow-x: hidden;
}

/* Global Button Styles */
.btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
    display: inline-block;
    text-decoration: none;
    border-radius: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-family: "Maven Pro", sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Button Sizes for Different Screen Sizes */
@media (min-width: 1440px) {
    .btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
        padding: 1.5rem 3rem;
        font-size: 1.2rem;
        min-width: 200px;
    }
}

@media (min-width: 1025px) and (max-width: 1439px) {
    .btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
        padding: 1.3rem 2.5rem;
        font-size: 1.1rem;
        min-width: 180px;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
        padding: 1.2rem 2.2rem;
        font-size: 1rem;
        min-width: 160px;
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    .btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
        padding: 1rem 2rem;
        font-size: 0.95rem;
        min-width: 140px;
    }
}

@media (max-width: 480px) {
    .btn, .cta-button, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn, .insights-cta-button, .careers-cta-button {
        padding: 0.9rem 1.8rem;
        font-size: 0.9rem;
        min-width: 120px;
    }
}

/* Button Color Variants */
.btn-orange, .hero-btn, .read-more-btn, .forum-btn, .conferences-btn {
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    color: white;
}

.btn-orange:hover, .hero-btn:hover, .read-more-btn:hover, .forum-btn:hover, .conferences-btn:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
    color: white;
    text-decoration: none;
}

.cta-button, .insights-cta-button, .careers-cta-button {
    background: white;
    color: #black;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
}

.cta-button:hover, .insights-cta-button:hover, .careers-cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #0072DA;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-decoration: none;
}
</style>

<?php get_footer(); ?>
