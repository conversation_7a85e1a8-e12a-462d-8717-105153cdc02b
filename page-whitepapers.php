<?php
/**
 * Template Name: Whitepapers Page
 * Description: Custom Whitepapers page with a grid UI similar to the Articles Section.
 * @package Krystelis_Custom
 */

get_header();
?>


<div class="articles-section">
    <div class="articles-container">
        <h2 class="articles-title">Whitepapers</h2>
        <div class="whitepapers-grid">
            <div class="whitepaper-card">
                <h3 class="whitepaper-title">Synthetic data and European General General Data Protection Regulation: Ethics, quality and legality of data sharing</h3>
                <p class="whitepaper-desc">
                    Synthetic data is increasingly being used across the financial services, clinical research, manufacturing and transport industries. Download this paper to learn more about the ethics, quality, and legality of data sharing.
                </p>
                <a href="<?php echo get_template_directory_uri(); ?>/assets/images/Paper-by-Shalini-published-in-Vol.6.4-JDPP-Summer-2024.pdf" class="whitepaper-download" target="_blank" rel="noopener"> &gt;&gt; DOWNLOAD WHITEPAPER</a>
            </div>
            <div class="whitepaper-card">
                <h3 class="whitepaper-title">EU CTR 536/2014: A harmonised approach to regulating clinical trials</h3>
                <p class="whitepaper-desc">
                    EU CTR harmonises the management and regulation of clinical trials in the EU. This white paper summarises the key changes from the existing regulation.
                </p>
                <a href="<?php echo get_template_directory_uri(); ?>/assets/images/White-Paper-EU-CTR-536-2014-A-Harmonised-Approach-to-Regulating-Clinical-Trials.pdf" class="whitepaper-download" target="_blank" rel="noopener"> &gt;&gt; DOWNLOAD WHITEPAPER</a>
            </div>
            <div class="whitepaper-card">
                <h3 class="whitepaper-title">EU CTR 536/2014: A guide to support implementation</h3>
                <p class="whitepaper-desc">
                    It is now imperative that sponsors determine how they will transition their operating models and clinical trials to comply with EU CTR 536/2014. This white paper discusses our recommendation for a structured approach to implementation.
                </p>
                <a href="<?php echo get_template_directory_uri(); ?>/assets/images/White-Paper-EU-CTR-536-2014-A-Guide-to-Support-Implementation.pdf" class="whitepaper-download" target="_blank" rel="noopener"> &gt;&gt; DOWNLOAD WHITEPAPER</a>
            </div>
            <div class="whitepaper-card">
                <h3 class="whitepaper-title">Riding the CTIS rollercoaster: What is the impact of the new transparency rules?</h3>
                <p class="whitepaper-desc">
                    Following a public consultation, EMA has recently revised the EU CTR transparency rules. This paper sheds light on these revisions.
                </p>
                <a href="<?php echo get_template_directory_uri(); ?>/assets/images/Riding-the-CTIS-rollercoaster-What-is-the-impact-of-the-new-transparency-rules.pdf" class="whitepaper-download" target="_blank" rel="noopener"> &gt;&gt; DOWNLOAD WHITEPAPER</a>
            </div>
        </div>
    </div>
</div>

<!--  Only this is needed -->
<div id="download-popup" class="download-popup-overlay" style="display:none;">
  <div class="download-popup-modal">
    <div class="download-popup-header">
      <span class="download-popup-title">Krystelis Download Center</span>
      <span class="download-popup-close" id="download-popup-close">&times;</span>
    </div>

    <!--  Add the correct ID and input IDs here -->
    <form class="download-popup-form" id="download-form">
      <input type="email" id="email" placeholder="Email Address" required>
      <input type="text" id="first-name" placeholder="First Name" required>
      <input type="text" id="last-name" placeholder="Last Name" required>
      <input type="text" id="designation" placeholder="Designation" required>
      <input type="text" id="company" placeholder="Company" required>
      <button type="submit" class="download-popup-btn">GET DOWNLOAD LINK</button>
    </form>

  </div>
</div>



<style>
.articles-section {
    background: #fafafa;
    padding: 40px 0 60px 0;
}
.articles-title {
    color: #1686e0;
    font-family: 'Maven Pro', Arial, sans-serif;
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 16px;
}

.whitepapers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(650px, 1fr));
    gap: 40px 32px;
    margin: 40px 40px 0 40px;
    padding: 0 16px;
}

.whitepaper-card {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.07);
    padding: 32px 28px 32px 28px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 350px;
    transition: box-shadow 0.2s;
}

.whitepaper-card:hover {
    box-shadow: 0 8px 32px rgba(22,134,224,0.13);
}

.whitepaper-title {
    color: #1686e0;
    font-family: 'Maven Pro', Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 12px;
}

.whitepaper-desc {
    color: #444;
    font-family: 'Maven Pro', Arial, sans-serif;
    font-size: 1rem;
    margin-bottom: 24px;
}

.whitepaper-download {
    color: #ff6c2d;
    font-family: 'Maven Pro', Arial, sans-serif;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    letter-spacing: 0.02em;
    margin-top: auto;
    transition: color 0.2s;
}

.whitepaper-download:hover {
    color: #1686e0;
}

/* Popup Overlay */
.download-popup-overlay {
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(22, 134, 224, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Popup Modal */
.download-popup-modal {
  background: #fff;
  border-radius: 16px;
  max-width: 500px;
  width: 95%;
  box-shadow: 0 8px 32px rgba(22,134,224,0.18);
  padding: 0 0 2.5rem 0;
  position: relative;
  animation: popupIn 0.2s;
}

@keyframes popupIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.download-popup-header {
  background: #4bb2ff;
  border-radius: 16px 16px 0 0;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.download-popup-title {
  color: #fff;
  font-size: 1.6rem;
  font-weight: 600;
  font-family: 'Maven Pro', Arial, sans-serif;
}

.download-popup-close {
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  margin-left: 1rem;
  transition: color 0.2s;
}

.download-popup-close:hover {
  color: #ff6c2d;
}

.download-popup-form {
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
  margin: 2.5rem 0 0 0;
  align-items: center;
}

.download-popup-form input {
  width: 90%;
  max-width: 420px;
  padding: 1.1rem 1.2rem;
  border: none;
  border-radius: 20px;
  background: #ededed;
  font-size: 1.15rem;
  color: #888;
  font-family: 'Maven Pro', Arial, sans-serif;
  margin: 0 auto;
  outline: none;
  text-align: left;
}

.download-popup-form input::placeholder {
  color: #bdbdbd;
  opacity: 1;
}

.download-popup-btn {
  width: 90%;
  max-width: 300px;
  margin: 1.2rem auto 0 auto;
  padding: 1.1rem 0;
  border: none;
  border-radius: 16px;
  background: linear-gradient(90deg, #ff914d 0%, #ff6c2d 100%);
  color: #fff;
  font-size: 1.15rem;
  font-weight: 700;
  font-family: 'Maven Pro', Arial, sans-serif;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 4px 16px rgba(255,108,45,0.13);
  text-transform: uppercase;
}

.download-popup-btn:hover {
  background: linear-gradient(90deg, #ff6c2d 0%, #ff914d 100%);
}
/* Tablet Styles - Fixed for 600px to 768px */
@media (min-width: 600px) and (max-width: 767px) {
    .articles-section {
        padding: 30px 0 50px 0;
    }

    .articles-title {
        font-size: 2.3rem;
        padding: 0 35px;
        margin-bottom: 18px;
    }

    .whitepapers-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        gap: 28px 24px;
        margin: 35px auto 0 auto;
        padding: 0 24px;
        max-width: 800px;
    }

    .whitepaper-card {
        background: #fff;
        border-radius: 13px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.07);
        padding: 26px 23px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 310px;
        transition: box-shadow 0.2s, transform 0.2s;
    }

    .whitepaper-card:hover {
        box-shadow: 0 8px 32px rgba(22,134,224,0.13);
        transform: translateY(-2px);
    }

    .whitepaper-title {
        color: #1686e0;
        font-family: 'Maven Pro', Arial, sans-serif;
        font-size: 1.35rem;
        font-weight: 700;
        margin-bottom: 13px;
        line-height: 1.3;
    }

    .whitepaper-desc {
        color: #444;
        font-family: 'Maven Pro', Arial, sans-serif;
        font-size: 0.98rem;
        margin-bottom: 23px;
        line-height: 1.5;
        flex-grow: 1;
    }

    .whitepaper-download {
        color: #ff6c2d;
        font-family: 'Maven Pro', Arial, sans-serif;
        font-size: 0.93rem;
        font-weight: 600;
        text-decoration: none;
        letter-spacing: 0.02em;
        margin-top: auto;
        transition: color 0.2s;
        display: inline-block;
        padding: 9px 0;
    }

    .whitepaper-download:hover {
        color: #1686e0;
    }

    /* Popup styles for tablet */
    .download-popup-overlay {
        padding: 20px;
    }

    .download-popup-modal {
        max-width: 460px;
        border-radius: 14px;
    }

    .download-popup-header {
        border-radius: 14px 14px 0 0;
        padding: 1.2rem 1.4rem;
    }

    .download-popup-title {
        font-size: 1.45rem;
    }

    .download-popup-close {
        font-size: 1.7rem;
        width: 30px;
        height: 30px;
    }

    .download-popup-form {
        gap: 1.08rem;
        margin: 2.2rem 0 0 0;
        padding: 0 28px;
    }

    .download-popup-form input {
        padding: 1.08rem 1.18rem;
        font-size: 1.08rem;
        border-radius: 17px;
        max-width: 380px;
    }

    .download-popup-btn {
        font-size: 1.08rem;
        border-radius: 13px;
        max-width: 280px;
        margin: 1.15rem auto 0 auto;
        padding: 1.08rem 0;
    }
}

/* Desktop Styles - Updated to start from 768px */
@media (min-width: 768px) {
    .articles-section {
        padding: 35px 0 55px 0;
    }

    .articles-title {
        font-size: 2.7rem;
        padding: 0 40px;
    }

    .whitepapers-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 35px 30px;
        margin: 38px auto 0 auto;
        padding: 0 32px;
    }

    .whitepaper-card {
        border-radius: 15px;
        padding: 30px 26px;
        min-height: 335px;
    }

    .whitepaper-title {
        font-size: 1.42rem;
    }

    .whitepaper-desc {
        font-size: 1.02rem;
        margin-bottom: 25px;
    }

    .whitepaper-download {
        font-size: 0.97rem;
    }

    .download-popup-modal {
        max-width: 490px;
        border-radius: 15px;
    }

    .download-popup-header {
        border-radius: 15px 15px 0 0;
        padding: 1.3rem 1.5rem;
    }

    .download-popup-title {
        font-size: 1.55rem;
    }

    .download-popup-close {
        font-size: 1.9rem;
    }

    .download-popup-form {
        gap: 1.15rem;
        margin: 2.3rem 0 0 0;
        padding: 0 32px;
    }

    .download-popup-form input {
        padding: 1.15rem 1.25rem;
        font-size: 1.12rem;
        border-radius: 19px;
    }

    .download-popup-btn {
        font-size: 1.12rem;
        border-radius: 15px;
        max-width: 310px;
        margin: 1.25rem auto 0 auto;
    }
}

        /* Desktop Styles */
        @media (min-width: 1024px) {
            .articles-section {
                padding: 40px 0 60px 0;
            }

            .articles-title {
                font-size: 3rem;
            }

            .whitepapers-grid {
                grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
                gap: 40px 32px;
                margin: 40px auto 0 auto;
                padding: 0 40px;
            }

            .whitepaper-card {
                border-radius: 16px;
                padding: 32px 28px;
                min-height: 350px;
            }

            .whitepaper-title {
                font-size: 1.5rem;
            }

            .whitepaper-desc {
                font-size: 1rem;
            }

            .download-popup-modal {
                max-width: 500px;
                border-radius: 16px;
                padding: 0 0 2.5rem 0;
            }

            .download-popup-header {
                border-radius: 16px 16px 0 0;
                padding: 1rem 1.5rem;
            }

            .download-popup-title {
                font-size: 1.6rem;
            }

            .download-popup-close {
                font-size: 2rem;
            }

            .download-popup-form {
                gap: 1.1rem;
                margin: 2.5rem 0 0 0;
                padding: 0;
            }

            .download-popup-form input {
                width: 90%;
                max-width: 420px;
                padding: 1.1rem 1.2rem;
                font-size: 1.15rem;
                border-radius: 20px;
            }

            .download-popup-btn {
                width: 90%;
                max-width: 300px;
                margin: 1.2rem auto 0 auto;
                padding: 1.1rem 0;
                font-size: 1.15rem;
                border-radius: 16px;
            }
        }

        /* Large Desktop Styles */
        @media (min-width: 1200px) {
            .whitepapers-grid {
                grid-template-columns: repeat(auto-fit, minmax(650px, 1fr));
                max-width: 1400px;
            }
        }

        /* Extra Large Screens */
        @media (min-width: 1440px) {
            .whitepapers-grid {
                grid-template-columns: repeat(auto-fit, minmax(700px, 1fr));
                max-width: 1600px;
            }
        }

        @media (min-width: 380px) and (max-width: 599px) {
    .articles-title {
        font-size: 2.2rem;
        padding: 0 30px;
        margin-bottom: 20px;
    }

    .whitepapers-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        padding: 0 20px;
        gap: 24px;
        margin: 32px auto 0 auto;
    }

    .whitepaper-card {
        padding: 26px 22px;
        border-radius: 12px;
        min-height: 300px;
    }

    .whitepaper-title {
        font-size: 1.3rem;
        margin-bottom: 14px;
    }

    .whitepaper-desc {
        font-size: 0.95rem;
        margin-bottom: 22px;
        line-height: 1.5;
    }

    .whitepaper-download {
        font-size: 0.92rem;
        padding: 10px 0;
    }

    .download-popup-overlay {
        padding: 18px;
    }

    .download-popup-modal {
        max-width: 420px;
        border-radius: 13px;
    }

    .download-popup-header {
        border-radius: 13px 13px 0 0;
        padding: 1.15rem 1.3rem;
    }

    .download-popup-title {
        font-size: 1.4rem;
    }

    .download-popup-close {
        font-size: 1.6rem;
        width: 28px;
        height: 28px;
    }

    .download-popup-form {
        padding: 0 25px;
        gap: 1.05rem;
        margin: 2.1rem 0 0 0;
    }

    .download-popup-form input {
        padding: 1.05rem 1.15rem;
        font-size: 1.05rem;
        border-radius: 17px;
        max-width: 360px;
    }

    .download-popup-btn {
        font-size: 1.05rem;
        border-radius: 13px;
        max-width: 260px;
        margin: 1.1rem auto 0 auto;
        padding: 1.05rem 0;
    }
}

        /* Very Small Mobile Devices */
        @media (max-width: 380px) {
            .articles-title {
                font-size: 1.75rem;
                padding: 0 15px;
            }

            .whitepapers-grid {
        display: grid;
        grid-template-columns: repeat(1, 1fr);
        padding: 0 0px;
        gap: 16px;
    }
            .whitepaper-card {
                padding: 20px 16px;
                border-radius: 10px;
                min-height: 260px;
            }

            .whitepaper-title {
                font-size: 1.2rem;
            }

            .whitepaper-desc {
                font-size: 0.9rem;
            }

            .download-popup-overlay {
                padding: 15px;
            }

            .download-popup-form {
                padding: 0 15px;
            }

            .download-popup-title {
                font-size: 1.2rem;
            }

            .download-popup-close {
                font-size: 1.3rem;
                width: 25px;
                height: 25px;
            }
        }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  let selectedDownloadUrl = ''; // To store the clicked whitepaper link

  // 1. When a whitepaper download link is clicked
  document.querySelectorAll('.whitepaper-download').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      selectedDownloadUrl = this.getAttribute('href'); // store the file URL
      document.getElementById('download-popup').style.display = 'flex';
    });
  });

  // 2. Close popup on (X) click
  document.getElementById('download-popup-close').onclick = function() {
    document.getElementById('download-popup').style.display = 'none';
  };

  // 3. Close popup if user clicks outside modal
  window.onclick = function(event) {
    var popup = document.getElementById('download-popup');
    if (event.target === popup) {
      popup.style.display = 'none';
    }
  };

  // 4. Handle form submission and open the selected file
  document.getElementById('download-form').addEventListener('submit', function(e) {
    e.preventDefault();

    // Simple validation (already handled by HTML5, but just in case)
    const email = document.getElementById('email').value.trim();
    const firstName = document.getElementById('first-name').value.trim();
    const lastName = document.getElementById('last-name').value.trim();
    const designation = document.getElementById('designation').value.trim();
    const company = document.getElementById('company').value.trim();

    if (email && firstName && lastName && designation && company) {
      document.getElementById('download-popup').style.display = 'none';
      
      if (selectedDownloadUrl) {
        window.open(selectedDownloadUrl, '_blank'); // Open file in new tab
      } else {
        alert('Download link is missing.');
      }
    } else {
      alert('Please fill in all required fields.');
    }
  });
});
</script>


<?php get_template_part('template-parts/cta-section'); ?>

<?php get_footer(); ?> 