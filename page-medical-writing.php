<?php
/**
 * Template for Medical Writing Page (UI Redesign)
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
  <div class="insights-hero-wrapper">
    <div class="insights-hero-content">
      <div class="hero-text">
        <h3 class="hero-subtitle">Making clinical research crystal clear</h3>
        <h1 class="hero-title">Medical Writing and Quality Review Services</h1>
        <p class="hero-description">
          Document writing for regulatory and commercial purposes is a fundamental capability supporting clinical
          research. Scientifically accurate and high-quality documents are important to ensure clear communication of
          messages to diverse audiences and stakeholders. Krystelis has the necessary scientific and industry experience
          to deliver the full range of writing services. We can support ad hoc documents or deliver through a functional
          service provider engagement. Our writing team has:
        </p>
        <ul class="hero-description-list" style="list-style: none; padding: 0; margin: 0;">
          <li>Extensive experience in providing writing services to the life sciences industry across therapeutic areas
          </li>
          <li>The capability and capacity to support you across a wide range of document types, throughout all phases of
            clinical development and commercialisation</li>
          <li>Skillsets to prepare logically organised and succinct scientific content</li>
          <li>Data visualisation approaches that effectively convey key messages to diverse audiences – regulatory,
            business, and the public, including clinical study participants</li>
          <li>A deep understanding of the interdependencies of disciplines (e.g., CMC, pharmacology, pharmacokinetics,
            clinical, regulatory) from a strategic planning perspective</li>
          <li>Robust project management and facilitation skills to help negotiate with internal and external
            stakeholders. We always deliver to deadlines</li>
        </ul>
      </div>
      <div class="hero-image">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Writing-Services-Image.png"
          alt="medical-writing-hero" />
      </div>
    </div>
  </div>

  <!-- Drug Development Process Section -->
  <section class="process-section">
    <h2 class="process-title">Our writing services across the drug development process</h2>
    <div class="process-image-wrapper">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-process-diagram.png"
        alt="Drug Development Process" class="process-image" id="openProcessModal"
        style="cursor: zoom-in; max-width: 100%; border-radius: 18px; box-shadow: 0 4px 24px rgba(0,0,0,0.08);" />
    </div>
  </section>

  <!-- Decorative Blue Triangle -->
  <img src="<?php echo get_template_directory_uri(); ?>/assets/images/your-blue-triangle.png" alt=""
    class="decorative-blue-triangle" loading="lazy" />

  <!-- Quality Review Services Section -->
  <section class="quality-review-hero-wrapper">
    <div class="quality-review-hero-content">
      <div class="quality-review-text">
        <h2 class="quality-review-title">Quality review services</h2>
        <p class="quality-review-description">
          Poor quality regulatory documents can impact drug development timelines and your company's reputation.
          Multiple reviewers, tight timelines, and changing requirements contribute to poor document quality. Document
          authors are typically responsible for ensuring quality. These authors are usually in high demand, are high
          cost, and may not have the time, experience, or skill sets required to do a thorough quality review. By using
          Krystelis's independent quality review services, you can increase the quality of clinical trial documents
          while accelerating drug approval timelines and reducing costs.
        </p>
      </div>
      <div class="quality-review-image">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Quality-Review-Services-Image.png"
          alt="Quality Review" />
      </div>
    </div>
    <h2 class="quality-review-includes">Our quality review service includes:</h2>
  </section>

  <!-- Quality Review Service Includes Section -->
  <section class="quality-review-includes-section">
    <!-- <div class="quality-review-includes-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50" fill="none"><circle cx="25" cy="25" r="25" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white\"></svg>
    </div> -->
    <div class="quality-review-includes-grid">
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        100% data verification against source documents
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        Validation using literature references
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        Formatting
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        Data integrity and internal consistency checks
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        Style and grammar/punctuation checks
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
        </span>
        Checking for overall completeness and clarity
      </div>
      <div class="quality-review-feature">
        <span class="quality-review-arrow">
          <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path xmlns="http://www.w3.org/2000/svg"
              d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z"
              fill="#FF9459" />
          </svg>
        </span>
        Compliance with template and authoring instructions
      </div>
    </div>
    <div class="quality-review-includes-note">
      We also provide editorial review services including proofreading, editing, and peer-reviewing.
    </div>
  </section>

  <!-- Section Divider SVG -->
  <div class="section-divider-svg">
    <svg viewBox="0 0 1920 120" width="100%" height="120" preserveAspectRatio="none" style="display: block;">
      <defs>
        <linearGradient id="dividerGradient" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#fff" />
          <stop offset="100%" stop-color="#f7f7f7" />
        </linearGradient>
      </defs>
      <polygon points="0,0 1920,0 1920,80 0,120" fill="url(#dividerGradient)" />
    </svg>
  </div>

  <!-- Benefits of Independent Quality Review Section -->
  <section class="benefits-quality-review-section">
    <h2 class="benefits-quality-review-title">Benefits of independent quality<br>review services delivered by Krystelis
    </h2>
    <div class="benefits-quality-review-grid">
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Extensive experience in providing quality review services</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Dedicated quality review team</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Review is fully independent of document authors</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Quick project start-up and an expedited approach for urgent requests</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Standardised process supported by robust checklists</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Approach optimised for each customer and document type</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Scalable engagements, from a single document to an entire submission</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">We can work directly with your internal authors or vendors</div>
      </div>
      <div class="benefit-card">
        <div class="benefit-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="benefit-text">Faster regulatory approvals</div>
      </div>
    </div>
  </section>

  <!-- What differentiates us -->
  <section class="what-diff-us-section">
    <h2 class="what-diff-us-title">What differentiates us
    </h2>
    <div class="what-diff-us-grid">
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Our proactive and collaborative approach</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Our comprehensive service offerings</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Our proven track record</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Strength of communication</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Deep understanding of current and emerging regulations</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Quality review conducted by an independent team on each draft prepared by Krystelis</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Findings documented and shared with clients</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Flexible and responsive resourcing model</div>
      </div>
      <div class="what-diff-us-card">
        <div class="what-diff-us-icon">
          <!-- Snowflake SVG -->
          <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
        </div>
        <div class="what-diff-us-text">Processes optimised for each project using our own or our client’s SOPs and checklists</div>
      </div>
    </div>
  </section>

  <?php get_template_part('template-parts/cta-section'); ?>

  <!-- Modal Popup -->
  <div id="processModal" class="process-modal">
    <span class="process-modal-close" id="closeProcessModal" title="Close">&times;</span>
    <img class="process-modal-content" id="processModalImg"
      src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-process-diagram.png"
      alt="Drug Development Process" />
  </div>
</main>

<?php get_footer(); ?>

<style>
  .site-main {
    min-height: 60vh;
    margin-top: -4.65551em;
    width: 100%;
  }

  .insights-hero-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
  }

  .insights-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
    max-width: 100%;
    margin: 0;
    padding: 0;
    gap: 0;
  }

  .hero-text {
    padding: 40px 60px 60px 40px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }

  .hero-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    color: #FF6A18;
    margin: 0 0 15px 0;
    line-height: 1.5;
  }

  .hero-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 30px 0 30px 0;
    line-height: 1.2;
  }

  .hero-description {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    line-height: 1.5;
    margin: 0 0 20px 0;
  }

  .hero-description-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .hero-description-list li {
    position: relative;
    padding-left: 2.2em;
    margin-bottom: 12px;
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    line-height: 1.5;
    margin: 0 0 20px 0;
    background: none;
  }

  .hero-description-list li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.2em;
    width: 1.5em;
    height: 1.5em;
    background-image: url('<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
    display: inline-block;
  }

  .hero-image {
    position: relative;
    background: #ffffff;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    overflow: hidden;
    padding: 15px;
    margin-top: 10rem;
  }

  .hero-image img {
    width: 100%;
    border-radius: 18px;
    display: block;
    margin-right: 3rem;
    object-fit: cover;
  }

  /* Modal Styles */
  .process-modal {
    display: none;
    position: fixed;
    z-index: 99999;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(30, 32, 36, 0.92);
    /* dark overlay */
    justify-content: center;
    align-items: center;
    transition: background 0.2s;
    overflow: auto;
    padding: 40px 32px;
    /* margin from top/bottom/left/right */
    box-sizing: border-box;
  }

  .process-title {
    color: #0072DA;
    font-family: 'Maven Pro', sans-serif;
    font-weight: 600;
    font-size: 3rem;
    margin: 1.5rem 50px 3rem 50px;
    text-align: center;
  }

  .process-modal-content {
    margin: auto;
    display: block;
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.28);
    background: #fff;
    object-fit: contain;
    transition: box-shadow 0.2s;
    padding: 0;
  }

  .process-modal-close {
    position: fixed;
    top: 32px;
    right: 40px;
    color: #fff;
    font-size: 2.8rem;
    font-weight: 700;
    cursor: pointer;
    z-index: 10001;
    border-radius: 50%;
    padding: 0 18px;
    line-height: 1.1;
    transition: background 0.2s, color 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  }

  .process-modal-close:hover {
    background: #fff;
    color: #222;
  }

  .quality-review-hero-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
  }

  .quality-review-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
    max-width: 100%;
    margin: 0;
    padding: 0;
    gap: 0;
  }

  .quality-review-text {
    padding: 40px 60px 60px 40px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }

  .quality-review-title {
    color: #0072DA;
    font-size: 3rem;
    font-family: 'Maven Pro', sans-serif;
    font-weight: 700;
    margin-bottom: 24px;
    line-height: 1.15;
  }

  .quality-review-description {
    font-size: 1.35rem;
    color: #525252;
    font-family: Georgia, serif;
    margin-bottom: 0;
    line-height: 1.6;
  }

  .quality-review-image {
    position: relative;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 15px;
  }

  .quality-review-image img {
    width: 100%;
    height: 500px;
    <!-- margin-right: 30px; -->
    border-radius: 24px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.10);
    object-fit: cover;
    display: block;
  }

  .quality-review-includes {
    text-align: center;
    color: #0072DA;
    font-size: 2.5rem;
    font-family: 'Maven Pro', sans-serif;
    font-weight: 700;
    margin: 80px 0 0 0;
  }

  .quality-review-includes-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0 0 60px 0;
    text-align: center;
    position: relative;
    z-index: 1;
  }

  .quality-review-includes-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    align-items: stretch;
    margin-left: 20px;
    margin-right: 20px;
  }

  .quality-review-feature {
    background: #fff;
    border-radius: 24px;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    /* blue shadow */
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    align-items: center;
    padding: 24px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: left;
  }

  .quality-review-feature:hover {
    box-shadow: 0 12px 40px rgba(65, 164, 255, 0.10);
  }

  .quality-review-arrow {
    display: flex;
    align-items: center;
    margin-right: 18px;
    flex-shrink: 0;
  }

  .quality-review-includes-note {
    color: #FF914D;
    font-size: 1.3rem;
    font-family: 'Maven Pro', sans-serif;
    margin: 48px 20px 0 20px;
    text-align: center;
  }

  .section-divider-svg {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    line-height: 0;
    z-index: 2;
    background: transparent;
  }

  .benefits-quality-review-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0 0 60px 0;
    text-align: center;
    z-index: 1;
  }

  .benefits-quality-review-title {
    color: #0072DA;
    font-size: 3rem;
    font-family: 'Maven Pro', sans-serif;
    font-weight: 700;
    margin: 60px 0 48px 0;
    line-height: 1.15;
  }

  .benefits-quality-review-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    align-items: stretch;
  }

  .benefit-card {
    background: #fff;
    border-radius: 24px;
    border: 1px solid #eaeaea;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 36px 36px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: center;
    position: relative;
    height: 100%;
    box-sizing: border-box;
  }

  .benefit-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);

  }

  .benefit-icon {
    position: absolute;
    top: -32px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }

  .benefit-text {
    margin-top: 32px;
    font-size: 1.35rem;
    color: #525252;
    font-family: Georgia, serif;
    line-height: 1.5;
  }

  .what-diff-us-section {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #fff;
  padding: 0 0 60px 0;
  text-align: center;
  z-index: 1;
}

.what-diff-us-title {
  color: #0072DA;
  font-size: 3rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  margin: 60px 0 48px 0;
  line-height: 1.15;
}

.what-diff-us-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  align-items: stretch;
}

.what-diff-us-card {
  background: #fff;
  border-radius: 24px;
  border: 1px solid #eaeaea;
  box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
  font-family: Georgia, serif;
  font-size: 1.6rem;
  color: #525252;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 36px 36px 36px;
  margin: 0;
  font-weight: 400;
  transition: box-shadow 0.2s;
  text-align: center;
  position: relative;
  height: 100%;
  box-sizing: border-box;
}

.what-diff-us-card:hover {
  box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);
}

.what-diff-us-icon {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.what-diff-us-text {
  margin-top: 32px;
  font-size: 1.35rem;
  color: #525252;
  font-family: Georgia, serif;
  line-height: 1.5;
}

/* Hero section mobile styles */
.insights-hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 8px;
}

.hero-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px 0 8px;
  text-align: center;
  width: 100%;
}

.hero-subtitle {
  order: 1;
  display: block !important;
  text-align: left;
  width: 100%;
  font-size: 1.25rem;
  color: #FF6A18;
  margin: 1.5rem 0 1.5rem 10px;
  font-weight: 500;
  line-height: 1.4;
}

.hero-image {
  order: 2;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 16px auto;
  padding: 0;
}

.hero-title {
  order: 3;
  font-size: 28px;
  text-align: left;
  margin: 20px auto 10px 10px;
}

.hero-description,
.hero-description-list {
  order: 4;
  font-size: 16px;
  text-align: left;
  margin-right: 10px;
  margin-left: 10px;
}

.hero-description-list li {
  position: relative;
  padding-left: 2.2em;
  margin-bottom: 12px;
  font-family: Georgia, serif;
  font-size: 18px;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 15px 10px;
  background: none;
}

.hero-image img {
  max-width: 90vw;
  margin-left: 0;
  border-radius: 12px;
}

  @media (max-width: 1024px) .insights-hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .hero-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 8px 0 8px;
    text-align: center;
    width: 100%;
  }

  .hero-subtitle {
    order: 1;
    display: block !important;
    text-align: left;
    width: 100%;
    font-size: 1.50rem;
    color: #FF6A18;
    margin: 2rem 0 2rem 30px;
    font-weight: 500;
    line-height: 1.4;
  }

  .hero-image {
    order: 2;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 16px auto;
    padding: 0;
  }

  .hero-title {
    order: 3;
    font-size: 36px;
    text-align: left;
    margin: 30px auto 10px 20px;
  }

  .hero-description,
  .hero-description-list {
    order: 4;
    font-size: 18px;
    text-align: left;
    margin-right: 20px;
    margin-left: 20px;
  }

  .hero-description-list li {
    position: relative;
    padding-left: 2.2em;
    margin-bottom: 12px;
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    line-height: 1.5;
    margin: 0 0 20px 20px;
    background: none;
  }


  .hero-image img {
    max-width: 95vw;
    margin-left: 45px;
    border-radius: 16px;
    margin-top: 5rem;
  }

  .process-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 60px 0 60px 0;
    text-align: center;
  }

  .process-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 2rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 40px;
    margin-left: 20px;
    margin-right: 20px;
  }

  .process-image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px
  }

  .process-image {
    width: auto;
    height: auto;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
    cursor: zoom-in;
    transition: box-shadow 0.2s;
  }

  .process-image:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  }

  @media (max-width: 1024px) {
    .quality-review-hero-content {
      grid-template-columns: 1fr;
      min-height: auto;
    }

    .quality-review-text {
      padding: 40px 30px;
      text-align: center;
    }

    .quality-review-image {
      min-height: 300px;
      padding: 15px;
    }

    .quality-review-image img {
      height: 400px;
      width: 100%;
      object-fit: cover;
      border-radius: 24px;
    }

    .quality-review-title {
      font-size: 2rem;
      text-align: left;
    }

    .quality-review-description {
      font-size: 1rem;
      text-align: left;
    }

    .quality-review-includes {
      font-size: 1.5rem;
      margin: 40px 0 0 0;
    }
  }

  <!-- @media (max-width: 1024px) {
    .quality-review-includes-grid {
      grid-template-columns: 1fr;
      gap: 18px 0;
      padding: 20px 4px 0 4px;
    }

    .quality-review-feature {
      font-size: 1.1rem;
      padding: 16px 12px;
    }

    .quality-review-includes-note {
      font-size: 1rem;
      margin: 28px 10px 0 10px;
    }
  } -->


  @media (max-width: 1024px) {
    .benefits-quality-review-title {
      font-size: 2rem;
      margin: 10px 20px 80px 20px;
    }

    .benefits-quality-review-grid {
      grid-template-columns: 1fr;
      gap: 50px 0;
      padding: 0 41px;
    }

    .benefit-card {
      font-size: 1.1rem;
      padding: 36px 12px 16px 12px;
    }

    .benefit-icon {
      width: 48px;
      height: 48px;
      top: -24px;
    }

    .benefit-text {
      margin-top: 24px;
      font-size: 1rem;
    }
  }


  @media (max-width: 1024px) {
    .differentiates-title {
      font-size: 2rem;
      margin: 40px 0 24px 0;
    }

    .differentiates-grid {
      grid-template-columns: 1fr;
      gap: 40px 0;
      padding: 0 30px;
    }

    .differentiates-card {
      font-size: 1.1rem;
      padding: 4px 12px 16px 12px;
    }

    .differentiates-icon {
      width: 48px;
      height: 48px;
      top: -24px;
    }

    .differentiates-text {
      margin-top: 32px;
      font-size: 1rem;
    }
  }

  @media (min-width: 1025px) and (max-width: 1440px) {
  <!-- .insights-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    gap: 48px;
    <!-- max-width: 1400px; -->
    <!-- margin: 0 auto;
    padding: 0 20px;
    min-height: 500px;
  }
  .hero-text { -->
    <!-- padding: 48px 40px 48px 40px; -->
    <!-- text-align: left;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .hero-subtitle {
    font-size: 1.50rem;
    color: #FF6A18;
    margin: 20px 0 24px 0;
    font-weight: 500;
    line-height: 1.4;
    text-align: left;
    width: 100%;
  }
  .hero-title {
    font-size: 2.8rem;
    text-align: left;
    margin: 30px 0 20px 0;
    font-weight: 700;
    color: #0072DA;
    line-height: 1.2;
  }
  .hero-description,
  .hero-description-list {
    font-size: 1.25rem;
    text-align: left;
    margin: 0 0 20px 0;
    color: #525252;
    line-height: 1.5;
  }
  .hero-description-list li {
    font-size: 1.25rem;
    margin: 0 0 20px 0;
    padding-left: 2.2em;
    position: relative;
    background: none;
  }
  .hero-image {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    margin: 0;
  }
    .hero-image img {
        max-width: 100%;
        border-radius: 25px;
        object-fit: cover;
        margin: 0;
        margin-top: 30px;
    }
} --> 

    .process-section {
      width: 100vw;
      position: relative;
      left: 50%;
      right: 50%;
      margin-left: -50vw;
      margin-right: -50vw;
      background: #fff;
      padding: 60px 0 60px 0;
      text-align: center;
    }

    .process-title {
      font-family: "Maven Pro", sans-serif;
      font-size: 2rem;
      font-weight: 600;
      color: #0072DA;
      margin-bottom: 40px;
      margin-left: 20px;
      margin-right: 20px;
    }

    .process-image-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .process-image {
      width: auto;
      height: auto;
      border-radius: 18px;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
      cursor: zoom-in;
      transition: box-shadow 0.2s;
    }

    .process-image:hover {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }

    /* Responsive styles for small devices */
  @media (max-width: 768px) {
  .quality-review-hero-wrapper {
    padding: 16px 0;
  }
  .quality-review-hero-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 16px;
    padding: 0 8px;
    min-height: unset;
  }
  .quality-review-text {
    padding: 16px 4px 8px 4px;
    text-align: center !important;
    align-items: center !important;
  }
  .quality-review-title {
    font-size: 2rem !important;
    text-align: center !important;
    margin-bottom: 40px;
  }
  .quality-review-description {
    font-size: 0.98rem !important;
    text-align: center !important;
    margin-bottom: 0;
  }
  .quality-review-image {
    min-height: unset;
    padding: 0;
    display: flex;
    justify-content: center;
  }
  .quality-review-image img {
    height: auto !important;
    width: 90vw !important;
    <!-- max-width: 350px; -->
    object-fit: cover;
    border-radius: 10px;
    margin: 0 auto;
  }
  .quality-review-includes {
    font-size: 2rem !important;
    margin: 16px 0 0 0;
    padding: 0 4px;
    text-align: center !important;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .quality-review-hero-wrapper {
    padding: 24px 0;
  }
  .quality-review-hero-content {
    display: flex !important;
    flex-direction: column-reverse; /* Change to 'row' if you want side-by-side */
    gap: 32px;
    padding: 0 20px;
    min-height: unset;
    align-items: center;
    justify-content: center;
  }
  .quality-review-text {
    padding: 24px 12px 24px 12px;
    text-align: left !important;
    align-items: flex-start !important;
  }
  .quality-review-title {
    font-size: 2.2rem !important;
    text-align: left !important;
    margin-bottom: 18px;
  }
  .quality-review-description {
    font-size: 1.08rem !important;
    text-align: left !important;
    margin-bottom: 0;
  }
  .quality-review-image {
    min-height: 220px;
    padding: 0 8px;
    display: flex;
    justify-content: center;
  }
  .quality-review-image img {
    height: 420px !important;
    width: 100% !important;
    max-width: 950px; /* Restrict image width on tablets */
    min-width: 650px;
    object-fit: cover;
    border-radius: 14px;
    margin: 0 auto;
  }
  .quality-review-includes {
    font-size: 1.35rem !important;
    margin: 40px 0 0 0;
    padding: 0 8px;
    text-align: center !important;
  }
}

@media (max-width: 768px) {
  .quality-review-includes-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
    padding: 0 8px !important;
    margin-left: 30px !important;
    margin-right: 30px !important;
    max-width: 100vw !important;
  }
  .quality-review-feature {
    font-size: 1rem !important;
    padding: 12px 8px !important;
    text-align: center !important;
    align-items: center !important;
  }
  .quality-review-includes-note {
    font-size: 1.25rem;
    margin: 24px 4px 0 4px;
    text-align: center;
  }
}

<!-- @media (min-width: 768px) and (max-width: 1024px) {
  .quality-review-includes-grid {
    grid-template-columns: 1fr 1fr !important;
    gap: 24px !important;
    padding: 0 20px !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: 100vw !important;
  }
  .quality-review-feature {
    font-size: 1.2rem !important;
    padding: 16px 12px !important;
    text-align: center !important;
    align-items: center !important;
  }
  .quality-review-includes-note {
    font-size: 1.35rem !important;
    margin: 52px 8px 0 8px !important;
    text-align: center !important;
  }
} -->
    
    @media (max-width: 1440px) {
      .quality-review-hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 500px;
        max-width: 100%;
        margin: 0;
        padding: 0;
        gap: 0;
      }
    }

    .quality-review-text {
      padding: 40px 60px 60px 40px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
    }

    .quality-review-image {
      min-height: 300px;
      padding: 15px;
    }

    .quality-review-image img {
      height: 400px;
      width: 100%;
      object-fit: cover;
      border-radius: 24px;
      margin-right: 10px;
    }

    .quality-review-title {
      font-size: 3rem;
      text-align: left;
    }

    .quality-review-description {
      font-size: 1.25rem;
      text-align: left;
    }

    .quality-review-includes {
      font-size: 3rem;
      margin: 30px 0 20px 0;
    }
  }

  <!-- @media (max-width: 1440px) {
    .quality-review-includes-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 40px;
      align-items: stretch;
      margin-left: 20px;
      margin-right: 20px;
    }

    .quality-review-feature {
      font-size: 1.6rem;
      color: #525252;
      display: flex;
      align-items: center;
      padding: 24px 36px;
      margin: 0;
      font-weight: 400;
      transition: box-shadow 0.2s;
      text-align: left;
    }

    .quality-review-includes-note {
      font-size: 1.3rem;
      margin: 48px 20px 0 20px;
      text-align: center;
    }
  } -->


  @media (max-width: 1440px) .benefits-quality-review-title {
    font-size: 2rem;
    margin: 10px 20px 80px 20px;
  }

  .benefits-quality-review-grid {
    gap: 50px 1.50rem;
    grid-template-columns: repeat(3, 1fr);
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    align-items: stretch;
  }

  .benefit-card {
    font-size: 1.6rem;
    padding: 8px 36px 36px 36px;
  }

  .benefit-icon {
    width: 48px;
    height: 48px;
    top: -24px;
  }

  .benefit-text {
    margin-top: 32px;
    font-size: 1.35rem;
  }

  /* Responsive styles for tablets */
@media (max-width: 900px) {
  .benefits-quality-review-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

/* Responsive styles for small devices */
@media (max-width: 768px) {
  .benefits-quality-review-section {
    padding: 24px 0;
  }
  .benefits-quality-review-title {
    font-size: 2rem;
    margin-bottom: 50px;
    padding: 0 10px;
  }
  .benefits-quality-review-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 8px;
  }
  .benefit-card {
    padding: 16px 8px;
    border-radius: 10px;
    margin: 0 30px 0 30px;
  }
  .benefit-icon svg {
    width: 48px;
    height: 48px;
  }
  .benefit-text {
    font-size: 0.98rem;
  }
}

/* Responsive styles for tablets */
@media (max-width: 900px) {
  .what-diff-us-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
  }
}

/* Responsive styles for small devices */
@media (max-width: 768px) {
  .what-diff-us-section {
    padding: 24px 0;
  }
  .what-diff-us-title {
    font-size: 2rem;
    margin-bottom: 50px;
    padding: 0 10px;
  }
  .what-diff-us-grid {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 0 8px;
  }
  .what-diff-us-card {
    padding: 16px 8px;
    border-radius: 10px;
    margin: 0 30px 0 30px;
  }
  .what-diff-us-icon svg {
    width: 48px;
    height: 48px;
  }
  .what-diff-us-text {
    font-size: 0.98rem;
  }
}
<style>
/* Desktop */
.quality-review-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  gap: 0;
  max-width: 100%;
  margin: 0;
  padding: 0;
}
.quality-review-includes-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 40px;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}
.quality-review-feature {
  font-size: 1.2rem;
  padding: 16px 12px;
  text-align: center;
  align-items: center;
}

/* Tablet */
@media (max-width: 1024px) {
  .quality-review-hero-content {
    grid-template-columns: 1fr;
    min-height: unset;
    gap: 32px;
    padding: 0 20px;
  }
  .quality-review-includes-grid {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 0 20px;
  }
}

/* Mobile */
@media (max-width: 768px) {
  .quality-review-hero-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0 8px;
  }
  .quality-review-includes-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 8px;
  }
  .quality-review-feature {
    font-size: 1rem;
    padding: 12px 8px;
  }
}

@media (min-width: 1000px) {
  .insights-hero-content {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    /* ...other desktop styles... */
  }
  .hero-text, .hero-image {
    width: 50%;
    /* ...other desktop styles... */
  }
}
@media (max-width: 999px) {
  .insights-hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .hero-text, .hero-image {
    width: 100%;
  }
}
</style>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    var modal = document.getElementById('processModal');
    var img = document.getElementById('openProcessModal');
    var closeBtn = document.getElementById('closeProcessModal');

    img.onclick = function () {
      modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
    closeBtn.onclick = function () {
      modal.style.display = 'none';
      document.body.style.overflow = '';
    }
    modal.onclick = function (e) {
      if (e.target === modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
      }
    }
  });

  document.addEventListener('DOMContentLoaded', function () {
    function moveHeroImageMobile() {
      var heroText = document.querySelector('.hero-text');
      var heroImage = document.querySelector('.hero-image');
      var heroSubtitle = document.querySelector('.hero-subtitle');
      var insightsHeroContent = document.querySelector('.insights-hero-content');
      
      if (window.innerWidth <= 999) {
        // Mobile: Move image after subtitle
        if (heroText && heroImage && heroSubtitle) {
          // Check if image is not already in the right position
          var nextSibling = heroSubtitle.nextElementSibling;
          if (nextSibling !== heroImage) {
            // Remove image from its current position
            if (heroImage.parentNode) {
              heroImage.parentNode.removeChild(heroImage);
            }
            // Insert image after subtitle
            heroSubtitle.parentNode.insertBefore(heroImage, heroSubtitle.nextSibling);
          }
        }
      } else {
        // Desktop: Move image back to original position
        if (insightsHeroContent && heroImage) {
          // Check if image is not already in the right position
          var lastChild = insightsHeroContent.lastElementChild;
          if (lastChild !== heroImage) {
            // Remove image from its current position
            if (heroImage.parentNode) {
              heroImage.parentNode.removeChild(heroImage);
            }
            // Append image to insights-hero-content
            insightsHeroContent.appendChild(heroImage);
          }
        }
      }
    }
    
    // Run on page load
    moveHeroImageMobile();
    
    // Run on window resize
    window.addEventListener('resize', moveHeroImageMobile);
  });
</script>