<?php
/**
 * Template Name: Training-2 Page
 * Description: Custom Training-2 page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<section class="training-hero-wrapper">
  <div class="training-hero-content">
    <div class="training-hero-text">
      <div class="training-hero-subtitle">Making clinical research crystal clear</div>
      <div class="training-hero-title">Krystelis LEARN</div>
      <div class="training-hero-description">
        <div class="training-hero-description">
  Krystelis designs and delivers effective training across various aspects of clinical research. We empower professionals with the knowledge and skills required to increase their effectiveness and develop their careers. Our experienced subject matter experts have developed a range of engaging and impactful courses and programs.
  If you want to grow your, or your team's, medical writing and clinical research skillsets, Krystelis LEARN can help you achieve this. 
  <a href="http://*************/training/#courses" class="highlight-link">Explore our training programs</a> to find out how these can help you achieve your career goals.
  <a href="http://*************/training/#form-header" class="highlight-link">Contact us</a> to learn more about our courses and the associated fees.
</div>

      </div>
    </div>
    <div class="training-hero-image">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Clinical-trial-transparency-services-Image.png" alt="Training" />
    </div>
  </div>
</section>

<div class="container">
        <div class="header" id="courses">
            <h1>Courses we offer</h1>
        </div>

        <div class="courses-grid">
            <div class="course-card">
                <div class="course-icon"><svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg></div>
                <div class="course-code">KL-CR01: Clinical Research Basics</div>                
                <p class="course-description">
                    This course covers the fundamental principles of clinical research for beginners or professionals seeking to enhance their knowledge on the topic.
                </p>
                <div class="course-buttons">
                    <a href="http://*************/training/#KL-CR01" class="btn btn-primary">See Course Details</a>
                    <a href="http://*************/training/#form-header" class="btn btn-secondary">Course Enquiry Form</a>
                </div>
            </div>

            <div class="course-card">
                <div class="course-icon"><svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg></div>
                <div class="course-code">KL-MW01: Regulatory Medical Writing</div>
                <p class="course-description">
                    A comprehensive course on regulatory medical writing covering regulatory guidelines and standards as well as the best practices for effective writing.
                </p>
                <div class="course-buttons">
                    <a href="http://*************/training/#KL-MW01" class="btn btn-primary">See Course Details</a>
                    <a href="http://*************/training/#form-header" class="btn btn-secondary">Course Enquiry Form</a>
                </div>
            </div>

            <div class="course-card">
                <div class="course-icon"><svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg></div>
                <div class="course-code">KL-MW02: Medical Communication</div>                
                <p class="course-description">
                    A medical communication course focused on the strategies and skills needed to effectively convey medical information to diverse audiences.
                </p>
                <div class="course-buttons">
                    <a href="http://*************/training/#KL-MW02" class="btn btn-primary">See Course Details</a>
                    <a href="http://*************/training/#form-header" class="btn btn-secondary">Course Enquiry Form</a>
                </div>
            </div>

            <div class="course-card">
                <div class="course-icon"><svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg></div>
                <div class="course-code">KL-MW03: Clinical Trial Transparency</div>                
                <p class="course-description">
                    The perfect course for anyone who wants to gain a deep understanding of the complex and ever-changing world of clinical trial transparency.
                </p>
                <div class="course-buttons">
                    <a href="http://*************/training/#KL-MW03" class="btn btn-primary">See Course Details</a>
                    <a href="http://*************/training/#form-header" class="btn btn-secondary">Course Enquiry Form</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Advantages of Clinical Research -->
<section class="clinical-research-advantages">
  <div class="clinical-research-advantages-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="clinical-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="clinical-bg-corner-img" />
  </div>
</section>


<section class="container">
  <div class="course-section" id="KL-CR01">

    <h1>KL‑CR01: Clinical Research Basics</h1>

    <p>The course is designed for beginners who are interested in pursuing a career in clinical research or for professionals seeking to enhance their knowledge in this field. It provides an in-depth introduction to the fundamental principles of clinical research and covers the entire process from study design to regulatory and ethical considerations.</p>

    <div class="section-title">Course Duration</div>
    <p>Individual participants can select either of the two models below.</p>

    <div class="bullet-list">
      <div class="bullet-item"><span>Mentor‑led (12 hours, covered over 6 weeks)</span></div>
      <div class="bullet-item"><span>Self‑paced (access to course material for 3 months)</span></div>
    </div>

    <p>To ask about training for your organisation, contact us at
      <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>.
    </p>

    <p>Upon completion of this course, participants will be able to:</p>

    <div class="bullet-list">
      <div class="bullet-item"><span>Understand the basic concepts and terminology of clinical research</span></div>
      <div class="bullet-item"><span>Evaluate different clinical‑trial designs</span></div>
      <div class="bullet-item"><span>Navigate the regulatory and ethical landscape of clinical research</span></div>
      <div class="bullet-item"><span>Communicate clinical‑research findings effectively with different stakeholders</span></div>
    </div>

    <div class="section-title">Course Structure</div>

    <!-- Module 1 -->
    <div class="module-title">Module 1: Basics of Clinical Research (4 h)</div>
    <div class="module-content">
      <div class="bullet-list">
        <div class="bullet-item"><span>History and evolution of clinical research</span></div>
        <div class="bullet-item"><span>Different phases of clinical research</span></div>
        <div class="bullet-item"><span>Clinical‑research ethics</span></div>
        <div class="bullet-item"><span>Clinical‑research design</span></div>
      </div>
    </div>

    <!-- Module 2 -->
    <div class="module-title">Module 2: Clinical‑Research Requirements (4 h)</div>
    <div class="module-content">
      <div class="bullet-list">
        <div class="bullet-item"><span>Good Clinical Practice (GCP)</span></div>
        <div class="bullet-item"><span>Institutional Review Boards / Ethics Committees</span></div>
        <div class="bullet-item"><span>Clinical‑trial monitoring and quality assurance</span></div>
        <div class="bullet-item"><span>Adverse‑event reporting and management</span></div>
      </div>
    </div>

    <!-- Module 3 -->
    <div class="module-title">Module 3: Regulatory‑Submission Processes (4 h)</div>
    <div class="module-content">
      <div class="bullet-list">
        <div class="bullet-item"><span>Overview of FDA, EMA, CDSCO & ICH guidelines</span></div>
        <div class="bullet-item"><span>Communication with regulatory agencies, sponsors, investigators, and participants</span></div>
        <div class="bullet-item"><span>Overview of submission documents</span></div>
      </div>
      <p><a href="http://*************/training/#form-header" class="cta-link">Click here</a> to sign up for this course.</p>
    </div>

  </div> 

  <div class="course-section" id="KL-MW01">
  <h1>KL‑MW01: Regulatory Medical Writing</h1>

  <p>This comprehensive course … (intro text unchanged).</p>
  <p>This course is ideal for aspiring medical writers …</p>

  <div class="section-title">Course Duration</div>
  <p>Individual participants can select either of the two models below.</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Mentor‑led (18 hours, covered over 12 weeks)</span></div>
    <div class="bullet-item"><span>Self‑paced (access to course material for 6 months)</span></div>
  </div>

  <p>
    To ask about training for your organisation, contact us at
    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>.
  </p>

  <p>Upon completion of this course, participants will be able to:</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Understand the role and importance of regulatory medical writing in the pharmaceutical industry</span></div>
    <div class="bullet-item"><span>Identify and describe the different types of regulatory documents</span></div>
    <div class="bullet-item"><span>Apply regulatory guidelines and standards in the creation of documents</span></div>
    <div class="bullet-item"><span>Develop skills in writing clear, concise, and accurate regulatory documents</span></div>
    <div class="bullet-item"><span>Collaborate effectively with cross‑functional teams in the preparation of regulatory submissions</span></div>
  </div>

  <div class="section-title">Course Structure</div>

  <!-- Module 1 -->
  <div class="module-title">Module 1: Introduction to Regulatory Medical Writing (2 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Introduction and scope of regulatory medical writing</span></div>
      <div class="bullet-item"><span>Overview of regulatory authorities and processes (FDA, EMA, CDSCO)</span></div>
      <div class="bullet-item"><span>Types of regulatory documents</span></div>
    </div>
  </div>

  <!-- Module 2 -->
  <div class="module-title">Module 2: Regulatory Guidelines and Standards (3 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>ICH E6: Good Clinical Practices</span></div>
      <div class="bullet-item"><span>ICH E3: Clinical Study Reports</span></div>
      <div class="bullet-item"><span>ICH M4E & M4S: Efficacy and Safety Summaries</span></div>
    </div>
  </div>

  <!-- Module 3 -->
  <div class="module-title">Module 3: Clinical Study Protocol & Informed Consent (2.5 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Writing a protocol and its amendments</span></div>
      <div class="bullet-item"><span>Writing an informed consent document</span></div>
      <div class="bullet-item"><span>Best practices in protocol and ICD writing</span></div>
    </div>
  </div>

  <!-- Module 4 -->
  <div class="module-title">Module 4: Investigator's Brochure (IB) (2 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Writing an IB</span></div>
      <div class="bullet-item"><span>Updating an IB</span></div>
      <div class="bullet-item"><span>Best practices in writing and updating an IB</span></div>
    </div>
  </div>

  <!-- Module 5 -->
  <div class="module-title">Module 5: Clinical Study Reports (CSR) (3 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Types of CSRs</span></div>
      <div class="bullet-item"><span>Structure of a full CSR</span></div>
      <div class="bullet-item"><span>Writing standalone and in‑text narratives</span></div>
      <div class="bullet-item"><span>Presentation of data – tables and figures</span></div>
      <div class="bullet-item"><span>Best practices in CSR writing</span></div>
    </div>
  </div>

  <!-- Module 6 -->
  <div class="module-title">Module 6: Common Technical Document (CTD) (4 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Overview of the CTD structure (Modules 1‑5)</span></div>
      <div class="bullet-item"><span>Writing non‑clinical summaries and overview</span></div>
      <div class="bullet-item"><span>Writing clinical summaries and overview</span></div>
      <div class="bullet-item"><span>Differences between regional requirements</span></div>
      <div class="bullet-item"><span>Best practices in preparing a coherent CTD dossier</span></div>
    </div>
  </div>

  <!-- Module 7 -->
  <div class="module-title">Module 7: Quality Control of Documents (1 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Peer review and quality control</span></div>
      <div class="bullet-item"><span>Proof‑reading techniques</span></div>
      <div class="bullet-item"><span>Responses to reviewers' comments and revisions</span></div>
      <div class="bullet-item"><span>Ensuring compliance with regulatory requirements</span></div>
    </div>
  </div>

  <!-- Module 8 -->
  <div class="module-title">Module 8: Project Management (1 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Working in a cross‑functional team</span></div>
      <div class="bullet-item"><span>Communication with stakeholders</span></div>
      <div class="bullet-item"><span>Conflict resolution and negotiations</span></div>
      <div class="bullet-item"><span>Timeline management</span></div>
    </div>
    <p><a href="http://*************/training/#form-header" class="cta-link">Click here</a> to sign up for this course.</p>
  </div>
</div>

  <!-- ───────────── KL‑MW02 ───────────── -->
  <div class="course-section" id="KL-MW02">
  <h1>KL‑MW02: Medical Communication</h1>

  <p>This course offers a comprehensive introduction to medical communications … (intro text unchanged).</p>

  <div class="section-title">Course Duration</div>
  <p>Individual participants can select either of the two models below.</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Mentor‑led (14 hours, covered over 8 weeks)</span></div>
    <div class="bullet-item"><span>Self‑paced (access to course material for 4 months)</span></div>
  </div>

  <p>
    To ask about training for your organisation, contact us at
    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>.
  </p>

  <p>Upon completion of this course, participants will be able to:</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Understand the key principles of medical communications</span></div>
    <div class="bullet-item"><span>Create and edit high‑quality medical content for various audiences and formats</span></div>
    <div class="bullet-item"><span>Apply ethical standards and regulatory guidelines in medical communications</span></div>
    <div class="bullet-item"><span>Develop effective communication strategies for scientific, educational, promotional, and patient‑centred content</span></div>
    <div class="bullet-item"><span>Utilise tools and techniques to enhance clarity and impact</span></div>
  </div>

  <div class="section-title">Course Structure</div>

  <!-- Module 1 -->
  <div class="module-title">Module 1: Introduction to Medical Communications (2 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Scope of medical communications</span></div>
      <div class="bullet-item"><span>Importance of effective medical communications</span></div>
      <div class="bullet-item"><span>Communicating with different stakeholders (HCPs, patients, regulators)</span></div>
    </div>
  </div>

  <!-- Module 2 -->
  <div class="module-title">Module 2: Scientific Publications (6 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Publication ethics</span></div>
      <div class="bullet-item"><span>Structure of a scientific manuscript</span></div>
      <div class="bullet-item"><span>Selecting journals</span></div>
      <div class="bullet-item"><span>Writing abstracts and titles</span></div>
      <div class="bullet-item"><span>Literature review and organising content</span></div>
      <div class="bullet-item"><span>Referencing styles</span></div>
      <div class="bullet-item"><span>Preparing manuscripts for submission</span></div>
      <div class="bullet-item"><span>The peer‑review process</span></div>
      <div class="bullet-item"><span>Addressing editors' comments</span></div>
      <div class="bullet-item"><span>Best practices in manuscript preparation</span></div>
    </div>
  </div>

  <!-- Module 3 -->
  <div class="module-title">Module 3: Developing Educational Material (4 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Medical information: standard response documents</span></div>
      <div class="bullet-item"><span>Communicating risk–benefit information</span></div>
      <div class="bullet-item"><span>Developing effective slide decks and videos</span></div>
      <div class="bullet-item"><span>Preparing visual aids and infographics</span></div>
      <div class="bullet-item"><span>Developing content for patient portals and apps</span></div>
    </div>
  </div>

  <!-- Module 4 -->
  <div class="module-title">Module 4: Effective Writing Techniques (2 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Use of AI in creating content</span></div>
      <div class="bullet-item"><span>Principles of clear and concise writing</span></div>
      <div class="bullet-item"><span>Common grammatical and stylistic errors</span></div>
      <div class="bullet-item"><span>Techniques for improving readability and engagement</span></div>
      <div class="bullet-item"><span>Use of tables, figures, and graphics</span></div>
      <div class="bullet-item"><span>Editing and proofreading strategies</span></div>
    </div>
    <p><a href="http://*************/training/#form-header" class="cta-link">Click here</a> to sign up for this course.</p>
  </div>
</div>


  <!-- ───────────── KL‑MW03 ───────────── -->
  <div class="course-section" id="KL-MW03">
  <h1>KL‑MW03: Clinical Trial Transparency</h1>

  <p>This comprehensive course on clinical trial transparency … (intro text unchanged).</p>

  <div class="section-title">Course Duration</div>
  <p>Individual participants can select either of the two models below.</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Mentor‑led (14 hours, covered over 8 weeks)</span></div>
    <div class="bullet-item"><span>Self‑paced (access to course material for 4 months)</span></div>
  </div>

  <p>
    To ask about training for your organisation, contact us at
    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>.
  </p>

  <p>Upon completion of this course, participants will be able to:</p>

  <div class="bullet-list">
    <div class="bullet-item"><span>Understand the importance of clinical trial transparency …</span></div>
    <div class="bullet-item"><span>Navigate regulatory requirements and guidelines …</span></div>
    <div class="bullet-item"><span>Develop and implement strategies for ensuring transparency …</span></div>
    <div class="bullet-item"><span>Utilise platforms and tools for registration, results reporting, and data sharing</span></div>
    <div class="bullet-item"><span>Address ethical and practical challenges associated with transparency</span></div>
  </div>

  <div class="section-title">Course Structure</div>

  <!-- Module 1 -->
  <div class="module-title">Module 1: Introduction to CTT (2 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>History and development of CTT requirements</span></div>
      <div class="bullet-item"><span>Scope of CTT</span></div>
      <div class="bullet-item"><span>Importance of CTT in clinical research</span></div>
    </div>
  </div>

  <!-- Module 2 -->
  <div class="module-title">Module 2: Regulatory Requirements of CTT (8 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>ClinicalTrials.gov, EU CTR 536/2014, WHO registry</span></div>
      <div class="bullet-item"><span>Regional differences (USA vs Europe)</span></div>
      <div class="bullet-item"><span>Regional registries</span></div>
      <div class="bullet-item"><span>Protocol registration & NIH review</span></div>
      <div class="bullet-item"><span>Results disclosure & NIH review</span></div>
      <div class="bullet-item"><span>Clinical Trial Information System (CTIS)</span></div>
      <div class="bullet-item"><span>EMA Policy 0070 & 0043</span></div>
      <div class="bullet-item"><span>Health Canada public release</span></div>
      <div class="bullet-item"><span>Basics of clinical trial data sharing</span></div>
      <div class="bullet-item"><span>Basics of plain‑language summaries</span></div>
    </div>
  </div>

  <!-- Module 3 -->
  <div class="module-title">Module 3: Ethical & Practical Considerations (4 h)</div>
  <div class="module-content">
    <div class="bullet-list">
      <div class="bullet-item"><span>Ethical principles in CTT; data protection requirements</span></div>
      <div class="bullet-item"><span>Balancing transparency with patient privacy</span></div>
      <div class="bullet-item"><span>Managing commercially confidential information</span></div>
    </div>
    <p><a href="http://*************/training/#form-header" class="cta-link">Click here</a> to sign up for this course.</p>
  </div>
</div>
</section>
<!-- End .course-section -->


        <!-- Advantages of Clinical Research -->
<section class="clinical-research-advantages">
  <div class="clinical-research-advantages-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="clinical-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="clinical-bg-corner-img" />
  </div>
</section>

<section class="form-container">
        <div class="form-header" id="form-header">
            <h1>Course Enquiry Form</h1>
        </div>
        
        <div class="form-content">
            <div class="form-group">
                <label for="name">Name <span class="required">*</span></label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="training-required">Training Required for: <span class="required">*</span></label>
                <div class="dropdown-container">
                    <div class="custom-select">
                        <select id="training-required" name="training-required" required>
                            <option value="">Select one option</option>
                        </select>
                    </div>
                    <div class="dropdown-options" id="training-dropdown">
                        <div class="dropdown-header">Select one option</div>
                        <div class="dropdown-option" data-value="individual-instructor">Individual (Instructor-led)</div>
                        <div class="dropdown-option" data-value="individual-self">Individual (Self-paced)</div>
                        <div class="dropdown-option" data-value="organisation-instructor">Organisation (Instructor-led)</div>
                        <div class="dropdown-option" data-value="organisation-self">Organisation (Self-paced)</div>
                    </div>
                </div>
                <div class="error-message" id="training-error" style="display: none;">
                    <svg class="error-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    This field is required.
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">Email <span class="required">*</span></label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone <span class="required">*</span></label>
                <input type="tel" id="phone" name="phone" required>
            </div>
            
            <div class="form-group full-width">
                <label>Select which training courses you are interested in: <span class="required">*</span></label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="course1" name="courses" value="KL-CR01">
                        <label for="course1">KL-CR01: Clinical Research Basics</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="course2" name="courses" value="KL-MW01">
                        <label for="course2">KL-MW01: Regulatory Medical Writing</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="course3" name="courses" value="KL-MW02">
                        <label for="course3">KL-MW02: Medical Communication</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="course4" name="courses" value="KL-MW03">
                        <label for="course4">KL-MW03: Clinical Trial Transparency</label>
                    </div>
                </div>
                <div class="error-message" id="courses-error" style="display: none;">
                    <svg class="error-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    This field is required.
                </div>
            </div>
            
            <div class="form-group">
                <label for="represents">Please select what represents you the best: <span class="required">*</span></label>
                <div class="dropdown-container">
                    <div class="custom-select">
                        <select id="represents" name="represents" required>
                            <option value="">Other (please specify)</option>
                        </select>
                    </div>
                    <div class="dropdown-options" id="represents-dropdown">
                        <div class="dropdown-header">Select one option</div>
                        <div class="dropdown-option" data-value="university">University student</div>
                        <div class="dropdown-option" data-value="researcher">Independent researcher</div>
                        <div class="dropdown-option" data-value="freelancer">Freelancer</div>
                        <div class="dropdown-option" data-value="clinical-individual">Clinical research professional (Individual)</div>
                        <div class="dropdown-option" data-value="clinical-company">Clinical research company</div>
                        <div class="dropdown-option" data-value="other">Other (please specify)</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="other-details">Other details:</label>
                <textarea id="other-details" name="other-details" maxlength="500" placeholder="Enter additional details..."></textarea>
                <div class="character-count">
                    <span id="char-count">0</span> of 500 max characters.
                </div>
            </div>
            
            <div class="form-group full-width">
                <button type="submit" class="submit-btn" onclick="submitForm()">Submit your course enquiry</button>
                <div class="mandatory-note">*Mandatory fields</div>
            </div>
        </div>
</section>
 
<!-- Get In Touch CTA Section Start -->
<section class="trainer-get-in-touch-cta">
  <div class="trainer-get-in-touch-bg">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Orange-1024x1024.png" alt="Decorative Orange Triangle" class="trainer-cta-triangle trainer-cta-triangle-left" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Blue-1024x1024.png" alt="Decorative Blue Triangle" class="trainer-cta-triangle trainer-cta-triangle-right" />
    <div class="trainer-get-in-touch-content">
      <div class="trainer-get-in-touch-heading">INTERESTED IN LEARNING MORE? </div>
      <div class="trainer-get-in-touch-title">Get In Touch</div>
      <a href="http://*************/contact-us/" class="trainer-get-in-touch-btn">CONTACT US</a>
    </div>
  </div>
</section>

<style>
.training-hero-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #fff;
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.training-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  max-width: 100%;
  margin: 0;
  padding: 0;
  gap: 0;
}

.training-hero-text {
  padding: 40px 60px 60px 40px;
  background: #fff;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}

.training-hero-subtitle {
  font-family: "Maven Pro", sans-serif;
  font-size: 1.75rem;
  font-weight: 500;
  color: #FF6A18;
  margin: 0 0 15px 0;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  display: inline-block;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

.training-hero-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
}

.training-hero-description {
  font-family: Georgia, serif;
  font-size: 20px;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.training-hero-image {
  position: relative;
  background: #ffffff;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 20px;
  margin-top: 7rem;
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

.training-hero-image img {
  width: 100%;
  border-radius: 20px;
  display: block;
  margin-right: 3rem;
  object-fit: cover;
  margin-bottom: 40px;
}

/* Keyframes reused */
@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.highlight-link {
  color: #9263D9; /* Replace with any color you want */
  text-decoration: none;
  font-weight: 600;
}

.highlight-link:hover {
  color: #FF6A18; /* Optional: a darker hover color */

}
/* Responsive styles */
@media (max-width: 999px) {
  .training-hero-content {
    display: flex;
    flex-direction: column;
    min-height: unset;
    width: 100%;
  }

  .training-hero-container {
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
    padding: 32px 16px !important;
    background: #fff !important;
  }

  .training-hero-subtitle {
    order: 1 !important;
    font-size: 1.25rem;
    text-align: center;
    margin: 10px 0 20px 0;
  }

  .training-hero-image {
    order: 2 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    padding: 0 10px;
    width: 100%;
  }

  .training-hero-image img {
    margin: 0 auto;
    display: block;
    border-radius: 12px;
    max-width: 100%;
    height: auto;
  }

  .training-hero-title {
    order: 3 !important;
    font-size: 2rem;
    text-align: center;
    margin: 20px 0;
  }

  .training-hero-description {
    order: 4 !important;
    font-size: 1rem;
    text-align: left;
    margin: 20px 0;
  }

  .training-hero-text,
  .training-hero-image {
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }

  .training-hero-text {
    padding: 32px 16px 32px 16px;
    align-items: left;
    text-align: left;
    display: flex;
    flex-direction: column;
  }
}

@media (max-width: 600px) {
  .training-hero-text {
    padding: 18px 4vw 18px 4vw;
  }

  .training-hero-subtitle {
    font-size: 1.25rem;
    margin-left: 10px;
    margin-top: 10px;
  }

  .training-hero-title {
    font-size: 2rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }

  .training-hero-description {
    font-size: 1rem;
    text-align: left;
    margin: 20px 4px 10px 4px;
  }

  .training-hero-image img {
    margin-right: 0;
    border-radius: 12px;
  }
}

.container {
            /* max-width: 1400px; */
            margin: 0 auto;
            /* padding: 0 20px; */
        }

        .header {
            text-align: center;
            margin-bottom: 10px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            color: #2563eb;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .course-card {
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-align: center;
            opacity: 0;
  transform: translateY(120px);
  transition: opacity 0.12s cubic-bezier(0.22, 1, 0.36, 1), transform 0.8s cubic-bezier(0.22, 1, 0.36, 1);
}

.course-card.animated {
  opacity: 1;
  transform: translateY(0);
}
        

        .course-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }

        .course-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        margin: -34px auto 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
}

        .course-icon::before {
            content: '';
            position: absolute;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            opacity: 0.2;
        }

        .course-icon::after {
            content: '';
            position: absolute;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, white 2px, transparent 2px);
            background-size: 8px 8px;
            opacity: 0.3;
        }

        .course-code {
             color: #f97316;
             font-size: 24px;
             font-weight: 600;
             letter-spacing: 0.5px;
             margin-bottom: 20px;
        }

        .course-description {
            color: #525252;
            font-size: 20px;
            line-height: 1.6;
            margin-bottom: 35px;
            text-align: left;
        }

        .course-buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .btn {
            padding: 14px 24px;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #2563eb);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #2563eb;
            border: 2px solid #2563eb;
        }

        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .courses-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .course-card {
                padding: 30px 20px;
            }
            
            .course-buttons {
                flex-direction: column;
            }
            
            .container {
                padding: 0 10px;
            }
        
        }

        @media (max-width: 480px) {
            .header {
                margin-bottom: 40px;
                padding: 20px 0;
            }
            
            .course-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 20px;
            }
            
            .course-title {
                font-size: 1.2rem;
            }
            
            .btn {
                padding: 12px 20px;
                font-size: 0.9rem;
            }
        }

        @media (min-width: 1200px) {
            .courses-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Animation for cards appearing */
        .course-card {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .course-card:nth-child(1) { animation-delay: 0.1s; }
        .course-card:nth-child(2) { animation-delay: 0.2s; }
        .course-card:nth-child(3) { animation-delay: 0.3s; }
        .course-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .clinical-research-advantages {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 260px;
}

.clinical-research-advantages-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transform: translateY(-60px);
  animation: advanceBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes advanceBgTopDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.clinical-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
}

.clinical-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  animation: advanceBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes advanceBgCornerRightToLeft {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Tablet styles (768px and below) */
@media screen and (max-width: 768px) {
  .clinical-research-advantages {
    min-height: 200px;
    padding: 0 0 32px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 250px;
  }

  .clinical-bg-corner-img {
    top: 30px;
    right: 12vw;
    width: 140px;
  }
}

/* Small tablet / Large mobile (600px and below) */
@media screen and (max-width: 600px) {
  .clinical-research-advantages {
    min-height: 180px;
    padding: 0 0 24px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 200px;
  }

  .clinical-bg-corner-img {
    top: 20px;
    right: 8vw;
    width: 120px;
  }
}

/* Mobile styles (480px and below) */
@media screen and (max-width: 480px) {
  .clinical-research-advantages {
    min-height: 160px;
    padding: 0 0 20px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 160px;
  }

  .clinical-bg-corner-img {
    top: 10px;
    right: 10vw;
    width: 100px;
  }
}

/* Small mobile (375px and below) */
@media screen and (max-width: 375px) {
  .clinical-research-advantages {
    min-height: 140px;
    padding: 0 0 16px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 140px;
  }

  .clinical-bg-corner-img {
    top: 8px;
    right: 11vw;
    width: 70px;
  }
}

/* Extra small mobile (320px and below) */
@media screen and (max-width: 320px) {
  .clinical-research-advantages {
    min-height: 120px;
    padding: 0 0 12px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 120px;
  }

  .clinical-bg-corner-img {
    top: 20px;
    right: 2vw;
    width: 70px;
  }
}

/* Large screens (1200px and above) */
@media screen and (min-width: 1200px) {
  .clinical-research-advantages {
    min-height: 300px;
    padding: 0 0 60px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 380px;
  }

  .clinical-bg-corner-img {
    top: 70px;
    right: 15vw;
    width: 220px;
  }
}

/* Extra large screens (1600px and above) */
@media screen and (min-width: 1600px) {
  .clinical-research-advantages {
    min-height: 350px;
    padding: 0 0 80px 0;
  }

  .clinical-research-advantages-bg-top {
    height: 450px;
  }

  .clinical-bg-corner-img {
    top: 120px;
    right: 12vw;
    width: 260px;
  }
}

/* KL-CR01: Clinical Research Basics) */
.container {
            margin: 0 auto;
        }

        h1 {
            font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
        }

        p {
            font-family: Georgia, serif;
  font-size: 20px;
  color: #525252;
  line-height: 1.5;
        }

        .section-title {
            font-family: Georgia, serif;
            font-size: 20px;
            color: #525252;
            line-height: 1.5;
            font-weight: bold;
            margin-top: 25px;
            margin-bottom: 10px;
        }

        .bullet-list {
            margin-left: 0;
            margin-bottom: 20px;
        }

        .bullet-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8px;
            font-size: 20px;
            color: #525252;
            font-family: Georgia, serif;
            line-height: 1.5;
        }

        .bullet-item::before {
            content: "▶";
            color: #FF6B35;
            font-size: 16px;
            margin-right: 12px;
            margin-top: 3px;
            flex-shrink: 0;
        }

        .email-link {
    color: #0072DA;
    text-decoration: none;
}

.email-link:hover {
    color: #FF6B35;
}

.cta-link {
    color: #0072DA;
    text-decoration: none;
}

.cta-link:hover {
    color: #FF6B35;
}
       
        .module-title {
            font-family: Georgia, serif;
            font-size: 20px;
            color: #525252;
            line-height: 1.5;
            font-weight: bold;
            margin-top: 25px;
            margin-bottom: 10px;
        }

        .module-content {
            margin-left: 0;
        }

        .indent {
            margin-left: 20px;
        }

        @media (max-width: 768px) {
          
            h1 {
                font-size: 20px;
            }
        }

        
        .form-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        .form-container::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 150px;
            clip-path: polygon(50% 0%, 100% 0%, 100% 100%);
        }

        .form-header {
            text-align: center;
            padding: 40px 20px;
            color: white;
            position: relative;
            z-index: 1;
        }

        .form-header h1 {
            font-size: 52px;
            font-weight: 600;
            font-family: "Maven Pro", sans-serif;
            margin: 0;
        }

        .form-content {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-size: 16px;
            font-weight: 700;
            color: rgb(0 0 0 / 85%);
            margin-bottom: 8px;
        }

        .required {
            color: #e74c3c;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
            font-family: Arial, sans-serif;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin: 0;
        }

        .checkbox-item label {
            font-size: 16px;
            font-weight: normal;
            font-family: "georgia", serif;
            margin: 0;
            cursor: pointer;
        }

        .custom-select {
            position: relative;
            width:100%;
        }

        .custom-select select {
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 20px;
            cursor: pointer;
             width:100%;
        }

        .dropdown-container {
            position: relative;
        }

        .dropdown-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #667eea;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 250px;
            overflow-y: auto;
            display: none;
        }

        .dropdown-options.show {
            display: block;
        }

        .dropdown-option {
            padding: 12px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f0f0f0;
        }

        .dropdown-option:hover {
            background-color: #f8f9ff;
        }

        .dropdown-option:last-child {
            border-bottom: none;
        }

        .dropdown-header {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            font-weight: 600;
            font-size: 14px;
        }

        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .error-icon {
            width: 16px;
            height: 16px;
        }

        .character-count {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .mandatory-note {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 20px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .form-content {
                grid-template-columns: 1fr;
                padding: 20px;
            }
            
            .form-header h1 {
                font-size: 2rem;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
        }
/* Get In Touch CTA */
.trainer-get-in-touch-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.trainer-get-in-touch-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
}
.trainer-get-in-touch-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.trainer-get-in-touch-heading {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 12px;
}
.trainer-get-in-touch-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.trainer-get-in-touch-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.trainer-get-in-touch-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.trainer-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.trainer-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.trainer-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .trainer-get-in-touch-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .trainer-get-in-touch-title {
    font-size: 2.2rem;
  }
  .trainer-get-in-touch-heading {
    font-size: 1.2rem;
  }
  .trainer-cta-triangle {
    width: 100px;
  }
  .trainer-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .trainer-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .trainer-get-in-touch-bg {
    min-height: 180px;
    padding: 0;
  }
  .trainer-get-in-touch-content {
    padding: 24px 4vw 24px 4vw;
  }
  .trainer-get-in-touch-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .trainer-get-in-touch-heading {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .trainer-get-in-touch-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .trainer-cta-triangle {
    width: 48px;
  }
  .trainer-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .trainer-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}

</style>


<script>
document.addEventListener('DOMContentLoaded', function () {
  // Function to reorder training-hero elements for small screens
  var originalTrainingStructureStored = false;
  var originalTrainingHeroHTML = '';

  function reorderTrainingHero() {
    var heroContent = document.querySelector('.training-hero-content');

    if (!heroContent) {
      console.log('Hero content not found');
      return;
    }

    // Store original structure on first run
    if (!originalTrainingStructureStored) {
      originalTrainingHeroHTML = heroContent.innerHTML;
      originalTrainingStructureStored = true;
      console.log('Original structure stored');
    }

    if (window.innerWidth <= 999) {
      // Check if already reordered
      if (heroContent.querySelector('.training-hero-container')) {
        console.log('Already reordered for mobile');
        return;
      }

      var heroSubtitle = document.querySelector('.training-hero-subtitle');
      var heroTitle = document.querySelector('.training-hero-title');
      var heroDescription = document.querySelector('.training-hero-text .training-hero-description');
      var heroImage = document.querySelector('.training-hero-image');

      console.log('Elements found:', {
        subtitle: !!heroSubtitle,
        title: !!heroTitle,
        description: !!heroDescription,
        image: !!heroImage
      });

      if (heroSubtitle && heroTitle && heroDescription && heroImage) {
        // Create a container for all elements
        var container = document.createElement('div');
        container.className = 'training-hero-container';
        container.style.cssText = 'display: flex; flex-direction: column; width: 100%; padding: 32px 16px; background: #fff;';

        // Add elements in the desired order:
        // 1. training-hero-subtitle
        // 2. training-hero-image
        // 3. training-hero-title
        // 4. training-hero-description(s)
        container.appendChild(heroSubtitle.cloneNode(true));
        container.appendChild(heroImage.cloneNode(true));
        container.appendChild(heroTitle.cloneNode(true));

        // Add all description elements
        heroDescriptions.forEach(function(desc) {
          container.appendChild(desc.cloneNode(true));
        });

        // Clear and add new structure
        heroContent.innerHTML = '';
        heroContent.appendChild(container);

        console.log('Training hero reordered for mobile - SUCCESS');
      } else {
        console.log('Missing elements, cannot reorder');
      }
    } else {
      // For larger screens: restore original structure
      if (originalTrainingStructureStored && heroContent.querySelector('.training-hero-container')) {
        heroContent.innerHTML = originalTrainingHeroHTML;
        console.log('Training hero restored for desktop');
      }
    }
  }

  // Run the reorder function with a small delay to ensure DOM is ready
  setTimeout(function() {
    reorderTrainingHero();
  }, 100);

  window.addEventListener('resize', reorderTrainingHero);

  // Debug: Log when function runs
  console.log('Training hero reorder script loaded. Screen width:', window.innerWidth);

  // Optional: Any additional JS logic that uses subtitle
  var subtitle = document.querySelector('.training-hero-subtitle');
  if (!subtitle) return;


// Add smooth scrolling and interactive effects
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Add a ripple effect
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                ripple.classList.add('ripple');
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
                
                // Simulate action
                console.log(`Clicked: ${this.textContent}`);
            });
        });

        // Add hover effects for course cards
        document.querySelectorAll('.course-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Handle custom dropdowns
        function setupDropdown(selectId, dropdownId) {
            const select = document.getElementById(selectId);
            const dropdown = document.getElementById(dropdownId);
            const options = dropdown.querySelectorAll('.dropdown-option');
            
            select.addEventListener('click', function(e) {
                e.preventDefault();
                dropdown.classList.toggle('show');
            });
            
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.getAttribute('data-value');
                    const text = this.textContent;
                    
                    // Update select value
                    select.value = value;
                    select.innerHTML = `<option value="${value}" selected>${text}</option>`;
                    
                    // Hide dropdown
                    dropdown.classList.remove('show');
                    
                    // Hide error if field was selected
                    const errorId = selectId + '-error';
                    const errorElement = document.getElementById(errorId);
                    if (errorElement) {
                        errorElement.style.display = 'none';
                    }
                });
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown-container')) {
                    dropdown.classList.remove('show');
                }
            });
        }
        
        // Initialize dropdowns
        setupDropdown('training-required', 'training-dropdown');
        setupDropdown('represents', 'represents-dropdown');
        
        // Character counter
        const textarea = document.getElementById('other-details');
        const charCount = document.getElementById('char-count');
        
        textarea.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
        
        // Form validation
        function validateForm() {
            let isValid = true;
            
            // Check training required
            const trainingRequired = document.getElementById('training-required');
            const trainingError = document.getElementById('training-error');
            if (!trainingRequired.value) {
                trainingError.style.display = 'flex';
                isValid = false;
            } else {
                trainingError.style.display = 'none';
            }
            
            // Check courses
            const courses = document.querySelectorAll('input[name="courses"]:checked');
            const coursesError = document.getElementById('courses-error');
            if (courses.length === 0) {
                coursesError.style.display = 'flex';
                isValid = false;
            } else {
                coursesError.style.display = 'none';
            }
            
            return isValid;
        }
        
        // Submit form
        function submitForm() {
            if (validateForm()) {
                alert('Form submitted successfully!');
            }
        }
        
        // Hide error messages when user interacts with fields
        document.querySelectorAll('input[name="courses"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const courses = document.querySelectorAll('input[name="courses"]:checked');
                const coursesError = document.getElementById('courses-error');
                if (courses.length > 0) {
                    coursesError.style.display = 'none';
                }
            });
        });



  // Helper: Animate elements on scroll into view
  function animateOnScroll(elements, delayStep = 200) {
    let observer = new IntersectionObserver((entries, obs) => {
      // Sort entries by DOM order for consistent stagger
      entries
        .filter(entry => entry.isIntersecting)
        .sort((a, b) => {
          return Array.prototype.indexOf.call(elements, a.target) - Array.prototype.indexOf.call(elements, b.target);
        })
        .forEach((entry, idx) => {
          setTimeout(() => {
            entry.target.classList.add('animated');
          }, idx * delayStep);
          obs.unobserve(entry.target);
        });
    }, { threshold: 0.2 });

    elements.forEach(el => observer.observe(el));
  }

  // Animate header h1
  var headerH1 = document.querySelectorAll('.header h1');
  if (headerH1.length) animateOnScroll(headerH1, 0);

  // Animate course cards with staggered delay
  var cards = document.querySelectorAll('.course-card');
  if (cards.length) animateOnScroll(cards, 200);


</script>

<?php get_footer(); ?> 