/*
Theme Name: Krystelis Custom WordPress Theme
Description: A custom WordPress theme replicating krystelis.com, featuring custom post types, ACF compatibility, and responsive design.
Author: Krystelis Development Team
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: krystelis-custom
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100%;
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    scroll-padding-top: 80px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
    /* Optimize rendering performance */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    will-change: scroll-position;
}

/* Container */
.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 15px;
    position: relative;
}

/* Site Content */
.site-content {
    width: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    overflow-x: hidden;
}

/* Page Container */
#page {
    width: 100%;
    overflow-x: hidden;
    position: relative;
}

/* Responsive Container Adjustments */
@media (min-width: 1024px) {
    .container {
        width: 100%;
        padding: 0 30px;
    }
}

@media (max-width: 1023px) {
    .container {
        width: 100%;
        padding: 0 15px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
}

/* Header */
.site-header {
    background: transparent;
    width: 100%;
    position: relative;
    margin: 0;
    padding: 30px;
}

/* Header Box Container */
.header-box {
    background: #fff;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    margin: 0 auto;
}

/* Top Bar - Hidden for clean design */
.header-top-bar {
    display: none;
}

/* Main Header */
.header-main {
    background: #fff;
    padding: 15px 0;
}

.site-branding {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.site-branding a img {
    max-width: 100%;
    height: auto;
    margin-top: 15px;
}

.site-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.site-title a {
    color: #333;
    text-decoration: none;
}

.site-description {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

/* Navigation */
.main-navigation {
    margin-top: 1rem;
}

.main-navigation ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.main-navigation a {
    color: #171c24 !important;
    text-decoration: none;
    font-weight: normal;
    font-family: "Maven Pro", sans-serif;
    font-size: 18.0018px;
    transition: color 0.3s ease;
}

.main-navigation a:hover {
    color: #007acc !important;
}

/* New Header Layout Styles */
.header-container {
    max-width: 100%;
    margin: 0;
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.site-branding {
    flex: 0 0 auto;
}

.site-logo {
    height: 40px;
    width: auto;
}

.logo-link {
    display: block;
}

.main-navigation {
    flex: 1;
    display: flex;
    justify-content: center;
    margin-top: 0;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 10px;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    color: #171c24 !important;
    text-decoration: none;
    font-weight: normal;
    font-family: "Maven Pro", sans-serif;
    font-size: 18.0018px;
    padding: 10px 0;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover,
.nav-menu .current-menu-item a {
    color: #007acc !important;
}

/* Additional specific navigation menu styling to override any conflicts */
#primary-menu a,
.menu a,
.nav-menu li a,
.main-navigation ul li a,
.site-header .main-navigation a,
.header-main .main-navigation a,
.header-container .main-navigation a {
    color: #171c24 !important;
    font-family: "Maven Pro", sans-serif;
    font-size: 18.0018px;
}

#primary-menu a:hover,
.menu a:hover,
.nav-menu li a:hover,
.main-navigation ul li a:hover,
.site-header .main-navigation a:hover,
.header-main .main-navigation a:hover,
.header-container .main-navigation a:hover,
#primary-menu .current-menu-item a,
.menu .current-menu-item a,
.nav-menu li.current-menu-item a,
.main-navigation ul li.current-menu-item a {
    color: #007acc !important;
}



/* Header Social Section */
.header-social {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    gap: 5px;
}

.connect-text {
    color: #525252;
    font-size: 16.0016px;
    font-family: Georgia, sans-serif;
    font-weight: 400;
    margin: 0px 4px 0px 0px;
}

.social-divider {
    color: #ccc;
    font-size: 16px;
}

.header-social .social-icons {
    display: flex;
    gap: 2px;
}

.social-link {
    display: block;
}

.header-social .social-icons .social-icon {
    width: 20px !important;
    height: 20px !important;
    transition: opacity 0.3s ease;
}

.header-social .social-icons .social-icon:hover {
    opacity: 0.7;
}

/* Twitter icon styling in header - ensure visibility */
.header-social .social-icons a[href*="twitter.com"] .social-icon {
    filter: none;
    opacity: 1;
}

/* Elementor Header Styling */
.elementor-111 .elementor-element.elementor-element-b5ac306 {
    margin-top: 250px;
    margin-bottom: 0px;
    transition: background 0.3s, border 0.3s, border-radius 0.3s, box-shadow 0.3s;
    transition-property: background, border, border-radius, box-shadow;
    transition-duration: 0.3s;
    transition-timing-function: ease;
    transition-delay: 0s;
    transition-behavior: normal;
    padding: 0px 25px;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.menu-icon {
    display: flex;
    flex-direction: column;
    width: 24px;
    height: 18px;
    justify-content: space-between;
}

.menu-icon span {
    display: block;
    height: 2px;
    width: 100%;
    background: #333;
    transition: all 0.3s ease;
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .site-header {
        padding: 10px;
    }

    .header-container {
        padding: 0 20px;
        flex-wrap: wrap;
    }

    .menu-toggle {
        display: block;
        order: 4;
    }

    .header-social {
        order: 2;
        margin-left: auto;
        margin-right: 15px;
    }

    .connect-text {
        display: none;
    }

    .social-divider {
        display: none;
    }

    .main-navigation {
        order: 4;
        width: 100%;
        justify-content: flex-start;
    }

    .nav-menu {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 1rem 20px;
        gap: 1rem;
        z-index: 999;
    }

    .main-navigation.toggled .nav-menu {
        display: flex;
    }

    .main-navigation.toggled .menu-icon span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .main-navigation.toggled .menu-icon span:nth-child(2) {
        opacity: 0;
    }

    .main-navigation.toggled .menu-icon span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Main Content */
.site-main {
    padding: 3rem 0;
    min-height: 60vh;
    width: 100%;
    overflow-x: hidden;
}

.content-area {
    display: grid;
    gap: 2rem;
    align-items: baseline;
    align-content: stretch;
    justify-items: stretch;
}

/* Posts */
.post {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #eee;
}

.post:last-child {
    border-bottom: none;
}

.entry-header {
    margin-bottom: 1rem;
}

.entry-title {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.entry-title a {
    color: #333;
    text-decoration: none;
}

.entry-title a:hover {
    color: #0073aa;
}

.entry-meta {
    color: #666;
    font-size: 0.9rem;
}

.entry-content {
    line-height: 1.7;
}

.entry-content p {
    margin-bottom: 1rem;
}

/* Sidebar */
.widget-area {
    background: #f9f9f9;
    padding: 1.5rem;
    border-radius: 5px;
}

.widget {
    margin-bottom: 2rem;
}

.widget:last-child {
    margin-bottom: 0;
}

.widget-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: #525252;
}

/* Footer */
.site-footer {
    background: #525252;
    color: #fff;
    padding: 2rem 0;
    margin-top: 2rem;
}

.footer-container {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0 0;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    align-items: start;
    justify-content: center;
}

.footer-logo img {
    max-width: 150px;
    height: auto;
}

.footer-logo-section img {
    width: 250px;
    height: auto;
    object-fit: contain;
    margin-left: 60px;
}

.footer-section h3 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #fff;
}

.border-left {
    border-left: 1px solid #555;
    padding-left: 2rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    width: 24px;
    height: 24px;
    transition: opacity 0.3s ease;
}

.social-icon:hover {
    opacity: 0.7;
}

.footer-bottom {
    background: #222;
    color: #ccc;
    text-align: center;
    padding: 1rem 0;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .footer-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 1024px) {
    .footer-container {
        width: 100vw;
        max-width: 100vw;
        grid-template-columns: 1fr;
        gap: 20px;
        text-align: center;
        justify-items: center;
        align-items: center;
        padding: 0 0;
    }
    .footer-section, .footer-logo-section {
        width: 100%;
        max-width: 100vw;
        box-sizing: border-box;
        margin: 0 auto 20px auto;
        text-align: center;
    }
    .footer-links-container {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    .contact-section {
        border: none;
        padding: 0;
    }
    .important-info-section ul {
        align-items: center;
    }
}

html, body, #page, .site-content {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
}

/* About Intro Section */
.about-intro {
    padding: 4rem 0;
    background-color: #fff;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1.5rem;
}

.about-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #666;
}

@media (max-width: 768px) {
    .about-intro {
        padding: 2rem 0;
    }

    .about-content h2 {
        font-size: 2rem;
    }

    .about-content p {
        font-size: 1rem;
    }
}

/* Utility Classes */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.alignleft {
    float: left;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.alignright {
    float: right;
    margin-left: 1rem;
    margin-bottom: 1rem;
}

.aligncenter {
    display: block;
    margin: 0 auto 1rem;
}

/* Comments */
.comments-area {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.comments-title {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
}

.comment-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.comment {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 5px;
}

.comment-author {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.comment-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
}

.comment-content p {
    margin-bottom: 1rem;
}

/* Search Form */
.search-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-field {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.search-submit {
    padding: 0.5rem 1rem;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.search-submit:hover {
    background: #005a87;
}

/* Pagination */
.pagination {
    margin: 2rem 0;
    text-align: center;
}

.page-numbers {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    background: #f9f9f9;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
}

.page-numbers:hover,
.page-numbers.current {
    background: #0073aa;
    color: #fff;
}

/* Footer Styling */
.site-footer {
  background: #f9f9f9;
  padding: 50px 0;
  font-family: 'Segoe UI', sans-serif;
  border-top: 1px solid #ddd;
  /* margin-left: 20px; */
}

.footer-logo-container {
  flex: 0 0 25%; /* Logo takes 25% of the footer width */
  padding-right: 25px;
  text-align: left; /* Align logo to the left */
}

.footer-logo-container img {
    width: 100; /* Make the logo take full width of its container */
    height: auto; /* Maintain aspect ratio */
    object-fit: contain; /* Ensure logo doesn't stretch */
}

.footer-section {
  flex: 1; /* Other sections take the remaining space */
  min-width: 200px;
  padding: 0;
}

.footer-section h3 {
  color: #0078d4;
  font-size: 22px;
  margin-bottom: 15px;
  font-weight: bold;
  position: relative;
  text-align: center;
  padding-bottom: 12px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: #0078d4;
}



.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin: 8px 0;
}

.footer-section ul li a,
.footer-section p a {
  text-decoration: none;
  color: #333;
  font-size: 18px;
}

.footer-section ul li a:hover,
.footer-section p a:hover {
  color: #0078d4;
}

/* Useful Links Grid Layout */
.useful-links-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.links-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.links-column ul li {
  margin: 8px 0;
}

.social-icons img.social-icon {
  width: 50px;
  height: 50px;
  border-radius: 10%;
  object-fit: cover;
  margin: 0 10px;
  transition: transform 0.3s ease;
}

.social-icons img.social-icon:hover {
  transform: scale(1.1);
}

.border-left {
  border-left: 1px solid #ccc;
  padding-left: 40px;
  margin-left: 40px;
}

.footer-bottom {
  text-align: center;
  padding: 10px 20px;
  background-color: #ffffff;
  color: #786868d4;
}

.footer-bottom p {
  margin: 0;
  font-size: 16px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .footer-container {
    flex-direction: column;
  }

  .footer-section {
    margin-bottom: 30px;
  }

  .footer-logo-container img {
    width: 500px; /* Adjust the logo size for smaller screens */
  }

  /* Mobile layout for useful links */
  .useful-links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 600px) {
    .footer-logo-section img {
        width: 180px;
        margin-left: 20px;
    }
}

@media (max-width: 480px) {
  .useful-links-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

/* New Footer Layout Styles */
.footer-logo-section {
  justify-content: center;
  margin-bottom: 30px;
}

.footer-logo-section img {
  width: 250px;
  height: auto;
  object-fit: contain;
}

/* Useful Links Section - 3 columns, 2 rows */
.footer-links-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 15px;
}

.footer-links-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-links-column a {
  text-decoration: none;
  color: #333;
  font-size: 18px;
  transition: color 0.3s ease;
  padding: 4px 0;
}

.footer-links-column a:hover {
  color: #0078d4;
}

/* Contact Section */
.contact-section {
  text-align: center;
  border-left: 1px solid #ddd;
  border-right: 1px solid #ddd;
  padding: 0 20px;
}

.contact-section .social-icons {
  display: flex;
  justify-content: center;
  gap: 3px;
  margin-top: 10px;
}

.contact-section .social-icons img {
  width: 40px;
  height: 40px;
  border-radius: 5px;
}

/* Important Information Section */
.important-info-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 7px;
}

.important-info-section ul li {
  margin: 0;
  text-align: center;
}

.important-info-section ul li a {
  text-decoration: none;
  color: #333;
  font-size: 18px;
  transition: color 0.3s ease;
  display: block;
  text-align: center;
  line-height: 1.1;
  padding: 0;
  margin: 0;
}

.important-info-section ul li a:hover {
  color: #0078d4;
}

/* Mobile responsive for new layout */
@media (max-width: 768px) {
  .footer-container {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .contact-section {
    border-left: none;
    border-right: none;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    padding: 20px 0;
  }

  .footer-links-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  .important-info-section ul {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .footer-links-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

@media (min-width: 1024px) and (max-width: 2060px) {
    .footer-logo img {
        margin-left: 30px;
    }
}

/* Webinars Page Styles */
.webinars-page-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 80px 20px;
    overflow: hidden;
}

.webinars-page-container {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    padding: 0 20px;
}

.webinars-page-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 56px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 60px 0;
    line-height: 1.2;
    text-align: center;
}

.webinars-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    margin: 40px auto;
    max-width: 1200px;
    padding: 0 20px;
}

.webinar-card {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    transition: all 0.3s ease;
    text-align: left;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
}

.webinar-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.webinar-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 32px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 20px 0;
    line-height: 1.3;
}

.webinar-speaker {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 18px 0;
}

.webinar-date {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 18px 0;
}

.webinar-time {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 28px 0;
}

.webinar-link {
    font-family: "Maven Pro", sans-serif;
    font-size: 22px;
    font-weight: 500;
    color: #ff7f3f;
    text-decoration: none;
    margin-top: auto;
    padding-top: 20px;
    transition: all 0.3s ease;
    text-align: left;
    display: inline-block;
}

.webinar-link:hover {
    color: #ff6b2b;
    text-decoration: underline;
    transform: translateX(5px);
}

/* Webinars Page Responsive Styles */
@media (max-width: 992px) {
    .webinars-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    .webinar-card {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .webinars-page-wrapper {
        padding: 60px 10px;
    }
    .webinars-page-title {
        font-size: 36px;
    }
    .webinar-title {
        font-size: 22px;
    }
    .webinar-speaker, .webinar-date, .webinar-time {
        font-size: 16px;
    }
    .webinar-link {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .webinar-card {
        padding: 18px 10px;
    }
}

/* Conferences CTA Section Styles */
.conferences-cta-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 4rem 1rem;
    background: #f8f9fa;
    overflow: hidden;
    margin-top: 80px;
}
