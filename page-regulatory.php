<?php
/**
 * Template Name: Regulatory Page
 * Description: Custom Regulatory page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>
<section class="regulatory-hero-wrapper">
  <div class="regulatory-hero-content">
    <div class="regulatory-hero-text">
      <div class="regulatory-hero-subtitle">Making clinical research crystal clear</div>
      <div class="regulatory-hero-title">Regulatory Services</div>
      <div class="regulatory-hero-description">
        At Krystelis, we understand that navigating the complex and ever-evolving landscape of pharmaceutical regulations is critical for the successful development, approval, and commercialisation of medical products. Our comprehensive regulatory services are designed to support pharmaceutical companies at every stage of the product lifecycle, from early development through post-market compliance. By leveraging our deep industry expertise and advanced technological solutions, we ensure that our clients can achieve timely market access, maintain regulatory compliance, and ultimately bring safe and effective products to patients around the world.
      </div>
    </div>
    <div class="regulatory-hero-image">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Regulatory-Services.png" alt="Regulatory Compliance" />
    </div>
  </div>
</section>

<Section class="regulatory-services-cards-wrapper">
  <div class="ctt-services-cards">
    <!-- Card 1 -->
    <div class="ctt-service-card">
      <div class="ctt-service-card-header">
        <span class="ctt-service-icon">
        <img src="https://krystelis.com/wp-content/uploads/2024/09/Regulatory-strategy-and-consulting.png"
         width="90" height="90"
         alt="Regulatory Strategy and Consulting Icon"
         style="display: block;" />
        </span>
        <span class="ctt-service-card-title">Regulatory Strategy and Consulting</span>
      </div>
      <div class="ctt-service-card-body">
        <ul class="ctt-service-list">
          <li><b>Regulatory Expertise:</b> Monitoring regulatory trends and providing expert guidance on opportunities and challenges that pharmaceutical companies encounter while navigating the evolving regulatory landscape.</li>
          <li><b>Regulatory Risk Assessment:</b> Identification and mitigation of potential regulatory risks throughout the product lifecycle.</li>
          <li><b>Global Regulatory Strategy:</b> Development of tailored regulatory strategies for global markets, including the US, EU, and emerging markets.</li>
        </ul>
      </div>
    </div>
    <!-- Card 2 -->
    <div class="ctt-service-card">
      <div class="ctt-service-card-header">
        <span class="ctt-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M79.5 63.037C79.4426 62.1567 78.858 61.5699 78.044 61.5699H78.0263C77.2013 61.5787 76.6233 62.181 76.5924 63.0701C76.5726 63.5929 76.5792 64.1224 76.5836 64.6343V64.9784C76.6167 69.0334 75.035 72.4265 71.8914 75.0607C70.7134 76.0469 69.5266 77.0617 68.3794 78.0435C67.8721 78.4759 67.3669 78.9105 66.8573 79.3429L60.3231 84.9091C60.2812 84.8893 60.2481 84.8738 60.2326 84.8606C59.0546 83.8568 57.8744 82.8574 56.6942 81.858C53.8484 79.4488 50.9056 76.958 48.0576 74.4562C45.4281 72.1463 44.0714 69.1261 44.0228 65.4836C43.9655 61.1992 43.9964 56.8398 44.0272 52.6238V52.4385C44.0294 52.2046 44.2258 51.8472 44.4155 51.7259C49.5974 48.4806 54.8147 45.2507 59.9172 42.1267C60.0959 42.0186 60.5194 42.0186 60.6981 42.1289C65.8293 45.275 71.0443 48.507 76.1954 51.7347C76.3718 51.845 76.5527 52.2046 76.5616 52.4605C76.5946 53.4621 76.5902 54.4836 76.5858 55.4698C76.5858 55.9507 76.5814 56.4317 76.5858 56.9126C76.5902 58.0025 77.1307 58.6555 78.0285 58.6621H78.044C78.4367 58.6621 78.7764 58.5319 79.0257 58.2849C79.3323 57.9826 79.4956 57.5149 79.5 56.9325C79.5022 56.3015 79.5 55.6705 79.5 55.0396C79.4956 54.0379 79.4933 52.9988 79.5132 51.9818C79.5353 50.8544 79.061 50.0139 78.0661 49.4182C77.3712 49.0012 76.6741 48.5644 75.9968 48.143C75.1122 47.5893 74.1989 47.0179 73.279 46.484C72.6988 46.1464 72.5025 45.8022 72.5047 45.1205C72.529 33.4432 72.5245 21.5717 72.5223 10.0885V5.48417C72.5223 3.72142 71.8142 3.00221 70.0803 3.00221C54.9647 3 39.8512 3 24.7356 3C23.0569 3 22.3686 3.68392 22.3575 5.35841C22.3531 6.15705 22.3531 6.95569 22.3575 7.73668V8.61034C22.3575 8.61034 22.3487 8.61475 22.3443 8.61695C22.3245 8.62578 22.3112 8.6324 22.298 8.63681C21.3141 8.64563 20.328 8.65225 19.3419 8.65446C17.3146 8.65887 16.6638 9.30749 16.6616 11.3262C16.6616 12.0718 16.6616 12.8175 16.6616 13.5456V14.3067L16.6373 14.3155C16.6109 14.3266 16.5932 14.3332 16.5954 14.3354C15.6049 14.3442 14.6144 14.3508 13.6239 14.353C11.59 14.3597 11.001 14.9443 11.001 16.9652V78.9921C11.001 79.1289 10.9988 79.2679 11.001 79.4069C11.0385 80.6578 11.7378 81.4013 12.9665 81.505C13.1055 81.516 13.2445 81.516 13.3791 81.516H21.6825C21.7906 81.516 21.8987 81.516 22.0046 81.5116C22.9686 81.4476 23.5642 80.885 23.562 80.0423C23.5598 79.1642 22.9532 78.6127 21.9803 78.6061C20.2155 78.5928 18.422 78.595 16.6881 78.5972C16.002 78.5972 15.3159 78.5972 14.6277 78.5972H13.9703V17.3402H58.2362C58.2428 17.3733 58.245 17.402 58.245 17.4218V19.6766C58.2494 26.1098 58.2516 32.7637 58.2274 39.305C58.2274 39.4948 58.031 39.8125 57.8413 39.9338C56.1206 41.0347 54.3536 42.1245 52.6439 43.1791L51.6115 43.8167C51.5145 43.8762 51.4086 43.9314 51.2961 43.9866C51.2696 43.4107 51.1505 43.06 50.91 42.8151C50.6232 42.5239 50.1842 42.4047 49.3989 42.4047C40.5329 42.4025 31.6691 42.4047 22.8032 42.4047H22.7392C22.5076 42.4047 22.2627 42.4047 22.0222 42.4488C21.2987 42.5856 20.8883 43.0688 20.8685 43.8123C20.8486 44.5646 21.2435 45.083 21.9516 45.2375C22.3311 45.3191 22.7237 45.3191 23.101 45.3191H23.1473C30.136 45.3213 37.1246 45.3213 44.1111 45.3213H48.9378C47.8216 46.1508 46.5884 46.9561 45.1038 47.8253C44.7464 48.0349 44.3008 48.1629 43.9103 48.1673C42.2646 48.1893 40.5903 48.1871 38.971 48.1871C37.9475 48.1871 36.9239 48.1871 35.9003 48.1893C34.8612 48.1938 34.2391 48.7321 34.2391 49.63C34.2391 50.5191 34.8546 51.0662 35.8892 51.0949C36.5488 51.1125 37.2195 51.1103 37.8658 51.1059C38.1438 51.1059 38.4218 51.1015 38.7019 51.1037H41.0712V53.9651H22.951C21.9869 53.9651 20.8773 54.1284 20.8597 55.3903C20.853 55.8051 20.97 56.1382 21.2082 56.3787C21.5457 56.7229 22.1127 56.8817 22.9929 56.8817C27.3078 56.8817 31.625 56.8817 35.94 56.8817H41.0447V59.7431H37.3937C32.5979 59.7431 27.8042 59.7431 23.0083 59.7431H22.8605C22.6002 59.7431 22.3333 59.7387 22.0686 59.7784C21.3428 59.8865 20.906 60.3697 20.8663 61.1022C20.8266 61.8898 21.2347 62.4303 21.9891 62.5847C22.3399 62.6575 22.6995 62.6575 23.048 62.6575H23.101C27.8042 62.6619 32.5074 62.6597 37.2129 62.6597H41.0425V65.5212H37.5901C32.7214 65.5212 27.8527 65.5212 22.9841 65.5212H22.8495C22.5848 65.5212 22.3112 65.5189 22.0421 65.5631C21.3251 65.6822 20.8972 66.172 20.8663 66.9088C20.8376 67.6567 21.2237 68.1818 21.9274 68.3495C22.2914 68.4355 22.6686 68.4355 23.0436 68.4355C28.3491 68.4399 34.6075 68.4421 40.802 68.4333H40.8285C41.2874 68.4333 41.4418 68.4598 41.5344 68.8701C42.3043 72.2213 44.1309 75.0872 46.9634 77.386C47.3363 77.6883 47.698 78.0038 48.1172 78.3722C48.1966 78.4406 48.2782 78.5134 48.3621 78.5862H44.8765C39.4718 78.5862 34.0671 78.5862 28.6623 78.5862H28.5145C28.2278 78.5862 27.9321 78.584 27.641 78.6303C26.9659 78.7406 26.5446 79.2017 26.485 79.8967C26.421 80.649 26.7784 81.1939 27.4689 81.3903C27.8439 81.4984 28.2586 81.4984 28.5895 81.4984H29.5778C36.5908 81.5028 43.8419 81.5094 50.974 81.4895H50.9828C51.574 81.4895 52.013 81.655 52.4542 82.0411C54.0558 83.4464 55.7676 84.9091 57.5435 86.3895C58.0112 86.7778 58.5119 87.1352 58.9973 87.4793C59.2201 87.6382 59.4429 87.7948 59.6613 87.9581L59.7186 88H60.8481L60.9033 87.9625C61.0599 87.8588 61.2209 87.7573 61.3797 87.658C61.7283 87.4396 62.0901 87.2146 62.4144 86.941C63.6983 85.8556 64.9932 84.7393 66.2462 83.6604C68.5625 81.666 70.9561 79.6055 73.3672 77.6486C77.894 73.9731 79.9015 69.19 79.4978 63.026L79.5 63.037ZM63.9652 20.3274C64.1439 21.1305 64.7064 21.5562 65.505 21.4901C66.3014 21.4261 66.7999 20.912 66.8021 20.1487C66.8176 17.4616 66.8286 13.925 66.7867 10.4018C66.7779 9.57885 66.2396 8.96333 65.3484 8.75374C64.9403 8.65887 64.5035 8.65887 64.1527 8.65887C53.0322 8.65446 41.9117 8.65666 30.7956 8.65446H25.3401V5.97615H69.5552V44.1608C69.3942 44.0616 69.2354 43.9645 69.0787 43.8696C68.3331 43.4152 67.6294 42.9849 66.9566 42.5084C66.9433 42.4996 66.8374 42.4069 66.8352 41.7914C66.8264 37.8136 66.8264 33.7675 66.8264 29.8581V26.5158C66.8264 25.5516 66.6654 24.4419 65.4146 24.4155C65.3991 24.4155 65.3859 24.4155 65.3704 24.4155C64.9756 24.4155 64.6557 24.5324 64.4196 24.764C64.0755 25.1016 63.9078 25.6818 63.9078 26.5378V40.642C63.738 40.5427 63.5725 40.4456 63.4093 40.3485C62.6548 39.9051 61.9423 39.4859 61.2672 39.0337C61.2584 39.0248 61.1768 38.9322 61.1746 38.4071C61.1658 32.4681 61.1658 26.529 61.168 20.5899V17.1439C61.168 16.9916 61.168 16.8394 61.168 16.6872C61.1591 15.0568 60.4665 14.3597 58.8494 14.3597C54.3668 14.3575 49.8864 14.3575 45.4038 14.3575H19.6177V11.6438H63.8725L63.877 11.7078C63.8902 11.8953 63.9056 12.0895 63.9056 12.2792C63.9056 13.5345 63.9056 14.7921 63.9078 16.0474C63.9078 17.1858 63.9078 18.3242 63.9078 19.4626V19.5729C63.9078 19.8222 63.9078 20.0781 63.963 20.3318L63.9652 20.3274ZM22.6355 30.8465C25.9158 30.8421 29.1962 30.8421 32.4765 30.8443H38.9115C42.3418 30.8443 45.7722 30.8443 49.2025 30.8443H49.3305C49.5974 30.8421 49.871 30.8421 50.1423 30.8884C50.8571 31.0098 51.2828 31.5039 51.3093 32.2408C51.3358 32.9843 50.9453 33.5072 50.2416 33.6748C49.8908 33.7587 49.5114 33.7587 49.2092 33.7587C41.8102 33.7609 34.4112 33.7609 27.01 33.7609H22.8804C22.8495 33.7609 22.8164 33.7609 22.7855 33.7609C22.737 33.7609 22.6862 33.7609 22.6377 33.7609C21.0582 33.7145 20.8464 32.7946 20.8619 32.2496C20.8751 31.7268 21.1266 30.8487 22.6377 30.8465H22.6355ZM60.2679 49.5859C57.1905 49.5903 54.2896 50.8059 52.099 53.0077C49.9107 55.2072 48.7128 58.1128 48.726 61.1838C48.7525 67.5442 53.9411 72.7045 60.301 72.7045H60.3385C63.4247 72.6957 66.3279 71.4823 68.5118 69.2893C70.6958 67.0964 71.8936 64.193 71.8826 61.1154C71.8605 54.755 66.661 49.5859 60.2856 49.5859H60.2679ZM68.9596 61.1C68.9817 63.3834 68.0993 65.5454 66.4779 67.189C64.8388 68.8503 62.657 69.7725 60.3319 69.7835C60.3165 69.7835 60.3032 69.7835 60.29 69.7835C57.9957 69.7835 55.836 68.8922 54.2014 67.2707C52.5601 65.6403 51.6512 63.4716 51.6468 61.1639C51.6358 56.4228 55.4853 52.5399 60.2282 52.5069C60.2481 52.5069 60.2679 52.5069 60.2878 52.5069C65.0307 52.5069 68.9133 56.35 68.9574 61.1H68.9596ZM51.2475 38.3167C51.1725 38.7424 50.7468 39.1638 50.3585 39.3955C50.1423 39.5234 49.8776 39.5455 49.6195 39.5455C49.5445 39.5455 49.4717 39.5455 49.3989 39.5411C49.3305 39.5411 49.2511 39.5345 49.1959 39.5367C40.4513 39.5411 31.7044 39.5411 22.962 39.5389H22.9024C22.6686 39.5345 22.4303 39.5389 22.1921 39.5058C21.3428 39.3889 20.8332 38.8175 20.8619 38.0144C20.8905 37.1981 21.4597 36.6576 22.3134 36.6355C23.3017 36.6135 24.3055 36.6157 25.2761 36.6179C25.6379 36.6179 25.9953 36.6179 26.3615 36.6179C29.1432 36.6179 31.925 36.6179 34.7046 36.6179H38.96C42.3749 36.6179 45.9046 36.6157 49.379 36.6289C50.0408 36.6311 50.4754 36.7392 50.7512 36.9687C51.0909 37.2511 51.3225 37.8776 51.2475 38.3078V38.3167ZM28.2542 25.3377C26.6747 25.2913 26.4629 24.3713 26.4784 23.8264C26.4916 23.3035 26.7409 22.4255 28.2498 22.4211C30.1536 22.4167 32.0574 22.4167 33.9612 22.4189H38.2784C40.1844 22.4211 42.0904 22.4211 43.9964 22.4189H44.0074C44.5545 22.4189 45.2229 22.5159 45.5692 23.2506C45.759 23.6499 45.759 24.1022 45.5737 24.4905C45.3906 24.8721 45.0464 25.1501 44.6273 25.2516C44.2809 25.3354 43.9169 25.3354 43.5927 25.3354C39.5909 25.3399 35.5892 25.3377 31.5897 25.3377H28.4947C28.4638 25.3377 28.4307 25.3377 28.3998 25.3377C28.3513 25.3377 28.3006 25.3377 28.252 25.3377H28.2542ZM21.2303 50.6316C20.9722 50.3624 20.8464 49.9984 20.8619 49.577C20.8928 48.721 21.4994 48.2048 22.4811 48.1938C23.5356 48.1827 24.6077 48.1849 25.6423 48.1871C26.099 48.1871 26.5578 48.1871 27.0122 48.1871C27.4446 48.1871 27.877 48.1871 28.3072 48.1871C29.3418 48.1871 30.4095 48.1827 31.4618 48.1915C32.5118 48.2004 33.1251 48.7144 33.1428 49.6013C33.1516 50.0161 33.0214 50.3757 32.7677 50.6382C32.481 50.9338 32.0508 51.0927 31.5213 51.0949C30.0168 51.1059 28.5212 51.1103 27.0475 51.1103C25.4923 51.1103 23.9591 51.1037 22.459 51.0927C21.9406 51.0883 21.5148 50.9294 21.2303 50.6316ZM59.84 61.7927C60.1664 61.4618 60.4885 61.1375 60.8062 60.8176C61.6886 59.9263 62.5203 59.0813 63.374 58.2407C64.0623 57.5634 64.8785 57.5149 65.5072 58.115C65.7984 58.393 65.9616 58.7526 65.9683 59.1254C65.9749 59.5093 65.816 59.8866 65.5182 60.191C64.0358 61.7111 62.4872 63.262 60.9143 64.7975C60.5966 65.1086 60.2392 65.263 59.8797 65.263C59.5201 65.263 59.1649 65.1086 58.845 64.8019C57.8435 63.8445 56.842 62.8428 55.8669 61.8258C55.2956 61.2301 55.2779 60.405 55.8228 59.8204C56.103 59.5203 56.4603 59.3482 56.8309 59.3372C57.217 59.3196 57.5832 59.4762 57.8987 59.7762C58.3994 60.2506 58.8605 60.7448 59.3502 61.2698C59.5113 61.4419 59.6745 61.6162 59.84 61.7905V61.7927Z" fill="white"/></svg>        </span>
        <span class="ctt-service-card-title">Regulatory Submissions and Documentation</span>
      </div>
      <div class="ctt-service-card-body">
        <ul class="ctt-service-list">
          <li><b>Preparation of Regulatory Submissions:</b> Authoring, compiling, and submitting high-quality regulatory submissions such as INDs, IMPDs, CTAs, NDAs, BLAs, MAAs, and more.</li>
          <li><b>eCTD Publishing and Submission:</b> Electronic submission of regulatory documents in eCTD format, ensuring compliance with regulatory agency requirements.</li>
          <li><b>Lifecycle Management:</b> Ongoing support for regulatory submissions, including post-approval changes, annual reports, and renewals.</li>
        </ul>
      </div>
    </div>
    <!-- Card 3 -->
    <div class="ctt-service-card">
      <div class="ctt-service-card-header">
        <span class="ctt-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M29.7798 18.8614H22.8025C21.7661 18.8614 22.0528 18.8614 21 18.8614C18.3468 18.8614 15.656 18.8567 12.9957 18.8402C12.5045 18.8379 11.7784 18.6827 11.3553 18.2548C10.9723 17.8693 10.7984 17.0653 10.984 16.5316C11.1532 16.0426 11.6609 15.7112 12.1826 15.4267C12.4646 15.2715 12.7842 15.295 13.0427 15.3138C13.1155 15.3185 13.1884 15.3256 13.2589 15.3256C21.3925 15.3256 25.5286 15.3256 33.6645 15.3256H37.2789C37.387 15.3256 37.4927 15.3256 37.6008 15.3256C38.7806 15.3632 39.5725 16.0755 39.5725 17.0982C39.5725 18.1467 38.7735 18.852 37.5867 18.8543C34.0969 18.859 33.2697 18.859 29.7798 18.859V18.8614ZM23.9728 24.5976C24.3582 24.6376 24.7389 24.6282 25.1103 24.6188L25.2771 24.6141H25.5779C25.7354 24.6141 25.8928 24.6188 26.0503 24.6211C26.2124 24.6258 26.3769 24.6282 26.5414 24.6282C26.7529 24.6282 26.9645 24.6211 27.1783 24.6023C28.0972 24.5177 28.6823 23.9135 28.7411 22.9872C28.8046 21.9975 28.2241 21.257 27.3311 21.1864C26.1866 21.0971 24.9998 21.1018 23.806 21.2029C22.9529 21.2758 22.3889 21.9904 22.4359 22.9426C22.4805 23.8688 23.0681 24.5012 23.9705 24.5953L23.9728 24.5976ZM43.2715 17.0958C43.2715 14.7285 44.2116 12.4928 45.913 10.8001C47.605 9.11684 49.8306 8.19293 52.1806 8.19293C52.1947 8.19293 52.2112 8.19293 52.2253 8.19293C57.0969 8.21644 61.0591 12.213 61.0568 17.1005C61.0568 19.4867 60.1309 21.7224 58.4529 23.3963C56.775 25.0701 54.5377 25.9893 52.1524 25.9893H52.1289C47.2384 25.9776 43.2645 21.9881 43.2692 17.0958H43.2715ZM57.5247 17.1311C57.5364 14.1784 55.1628 11.7546 52.23 11.7264C52.2135 11.7264 52.1971 11.7264 52.1806 11.7264C50.7659 11.7264 49.4263 12.2859 48.4017 13.3038C47.3677 14.3312 46.8013 15.6806 46.8037 17.1076C46.8107 20.0533 49.2148 22.4512 52.1665 22.4559H52.1736C55.1111 22.4559 57.5106 20.0697 57.5247 17.1311ZM61.0215 59.96C60.7113 62.4638 58.5305 64.3774 55.9454 64.4127C54.4954 64.4315 53.0337 64.4362 51.5813 64.4362C50.6202 64.4362 49.6613 64.4338 48.7166 64.4315C47.7319 64.4291 46.7473 64.4268 45.7626 64.4268C42.6958 64.4268 39.6289 64.4268 36.5621 64.4268H30.3885C25.3265 64.4291 21.423 64.4315 16.274 64.4221C14.6595 64.4197 13.2213 63.832 12.2249 62.7694C11.2543 61.735 10.7702 60.3221 10.8642 58.7893C11.0075 56.4642 12.9205 54.4142 15.2188 54.1204C15.6348 54.0686 16.079 54.0451 16.6641 54.0451C30.2052 54.0428 40.7335 54.0404 55.1957 54.0498C55.8162 54.0498 56.5329 54.0639 57.205 54.2356C59.7455 54.8797 61.3482 57.287 61.0168 59.9624L61.0215 59.96ZM55.9642 57.6044C55.6799 57.5786 55.3908 57.5786 55.0876 57.5786H54.9654C49.377 57.5786 43.7909 57.5786 38.2024 57.5786H32.7151C26.9927 57.5786 22.6027 57.5786 16.8827 57.5786H16.831C16.4738 57.5786 16.1377 57.5786 15.8111 57.6185C14.9415 57.7267 14.3611 58.4225 14.394 59.3088C14.4457 60.6159 15.5996 60.8886 16.5584 60.8886C29.3921 60.8886 38.2283 60.8886 51.0596 60.8886H55.4918C55.6658 60.891 55.8303 60.8933 55.9807 60.8745C56.9207 60.7687 57.5388 60.087 57.52 59.1795C57.5012 58.305 56.8925 57.6844 55.9642 57.6021V57.6044ZM11.0757 72.7467C10.5822 71.1598 10.843 69.4977 11.7972 68.1906C12.7983 66.8176 14.4057 66.0207 16.2082 66.0042C18.4925 65.9831 20.812 65.9878 23.0587 65.9925C24.0527 65.9925 23.7143 65.9972 24.706 65.9972H30.5154C35.3918 65.9972 37.6008 65.9925 42.4772 65.9995C45.5699 66.0042 47.8824 68.1107 47.9764 71.007C48.0234 72.4293 47.5228 73.7599 46.5687 74.752C45.5605 75.8005 44.1387 76.3812 42.5642 76.3859C40.4867 76.3953 38.3881 76.3976 36.3059 76.3976C34.8207 76.3976 33.3449 76.3976 31.8902 76.3953C30.4096 76.3953 31.5917 76.3929 30.1112 76.3929H25.7283C22.2009 76.3976 19.8861 76.4 16.2975 76.3882C13.7759 76.3812 11.776 74.9847 11.078 72.7443L11.0757 72.7467ZM14.4128 71.5524C14.6431 72.6832 15.7476 72.8524 16.3845 72.8548C26.4098 72.8595 32.4377 72.8571 42.4631 72.8548C42.6793 72.8548 42.8838 72.836 43.0694 72.8031C43.8849 72.6526 44.4489 71.9873 44.4442 71.188C44.4395 70.4098 43.8943 69.761 43.1211 69.6058C42.8086 69.5447 42.4678 69.5377 42.0871 69.5377C38.1695 69.533 34.252 69.533 30.3344 69.5353H19.832C19.4701 69.5283 19.0894 69.533 18.7181 69.5306C18.3585 69.5283 17.9943 69.5259 17.63 69.5259C17.1224 69.5259 16.6124 69.5306 16.1119 69.5494C15.5244 69.5706 15.0261 69.8033 14.7089 70.2077C14.4175 70.5791 14.3117 71.0563 14.4128 71.5547V71.5524ZM10.9112 48.1679C10.4835 45.6806 11.863 43.2733 14.1919 42.4457C14.8287 42.22 15.5737 42.1072 16.4714 42.1049C22.1891 42.0884 26.6707 42.0884 32.292 42.0907H34.0452C36.2119 42.0907 38.3787 42.0907 40.5431 42.0907C45.6545 42.0907 50.7635 42.0884 55.8749 42.0978C58.044 42.1025 59.6726 43.1393 60.5797 45.0952C61.4634 47.0018 61.2213 48.8872 59.8818 50.5517C58.8689 51.8071 57.4659 52.4442 55.7104 52.4442H55.7034C49.4146 52.4371 43.1282 52.4371 36.8394 52.4395H32.5388C30.8797 52.4395 29.2323 52.4395 27.5778 52.4395C23.3313 52.4395 20.2738 52.4442 15.9544 52.4301C13.4587 52.4207 11.3389 50.6293 10.9135 48.1655L10.9112 48.1679ZM16.3939 48.9107C21.698 48.9131 25.6719 48.9131 30.976 48.9107H38.6725C44.1716 48.9107 49.6731 48.9107 55.1722 48.9107H55.379C55.6235 48.9154 55.8538 48.9154 56.0747 48.8896C56.9348 48.7885 57.5411 48.0809 57.5176 47.2063C57.4847 45.8992 56.3872 45.6242 55.473 45.6242C43.0412 45.6242 34.6045 45.6242 22.1703 45.6242H16.3422C16.1753 45.6242 16.0179 45.6242 15.8675 45.6383C15.0026 45.7182 14.3822 46.4094 14.3893 47.2816C14.3987 48.316 15.1295 48.9084 16.3915 48.9084L16.3939 48.9107ZM10.8665 22.9896C10.9488 23.9958 11.5904 24.5906 12.6244 24.6211C13.1343 24.6376 13.6467 24.6329 14.1449 24.6282H14.8029C15.0238 24.6282 15.2447 24.6282 15.4633 24.6305C15.6818 24.6305 15.9051 24.6352 16.1307 24.6352C16.415 24.6352 16.6994 24.6329 16.9837 24.6235C18.0695 24.5858 18.7698 23.944 18.8074 22.9449C18.8262 22.4489 18.6664 21.9928 18.3538 21.6613C18.0413 21.3275 17.5924 21.1347 17.0848 21.1206C15.5361 21.0736 14.0109 21.0759 12.5539 21.123C12.0416 21.1394 11.588 21.3416 11.2778 21.6895C10.9723 22.0328 10.8242 22.4959 10.8665 22.9896ZM87.9956 77.519C87.965 79.884 86.4727 81.6637 84.1931 82.0492C83.7795 82.1197 83.709 82.2185 83.6432 82.5735C83.4764 83.4856 83.2625 84.4377 83.0087 85.404C82.2802 88.1545 80.144 89.8683 77.291 89.9859C77.1242 89.9929 76.955 89.993 76.7905 89.9906H66.1776C45.2033 89.9953 28.2288 89.9977 7.25449 90C4.52842 90 2.60137 88.5448 2.1008 86.1092C2.01385 85.6908 2.0068 85.2606 2.0068 84.8938C2.00445 66.7495 2.00445 48.6075 2.0068 30.4631C1.9974 30.1693 1.99975 29.8613 2.0021 29.5627C2.0021 29.4193 2.00445 29.2759 2.00445 29.1349V25.183C2.0021 25.0631 1.99975 24.9432 2.00445 24.8256C2.00445 18.497 2.00445 8.24935 2.00445 5.13204C2.0068 2.01708 4.01845 0 7.12994 0C27.6859 0 44.2445 0 64.7981 0C67.9002 0 69.9024 2.02179 69.9048 5.1485C69.9071 14.4393 69.9071 23.7301 69.9048 33.0209V44.0726C70.0011 44.0702 70.0998 44.0655 70.1962 44.0608C70.7414 44.0397 71.3054 44.0161 71.8577 44.1031C74.391 44.4957 76.5414 45.8193 77.9185 47.8293C79.3074 49.8582 79.7656 52.376 79.2087 54.9173C79.0136 55.8083 78.8209 56.6993 78.6282 57.5903C78.2569 59.3018 77.8738 61.0744 77.479 62.8117C77.4109 63.1103 77.4391 63.1291 77.6224 63.2537C78.5177 63.8531 78.9854 64.7841 78.976 65.9455C78.969 66.907 78.969 67.8685 78.9713 68.8653V69.7445H79.9959C81.0206 69.7445 82.0146 69.7445 83.0111 69.7445C86.1742 69.7492 87.9909 71.5618 87.9956 74.7214V75.3985C88.0003 76.0897 88.0026 76.8067 87.9956 77.5119V77.519ZM61.2236 82.2255C61.2495 82.3407 61.273 82.4536 61.2965 82.5664C61.4375 83.2129 61.5691 83.8242 61.7195 84.433C62.0438 85.7401 62.9016 86.436 64.2012 86.4454C68.4548 86.476 72.8071 86.476 77.1359 86.4454C77.9678 86.4384 79.0489 86.1492 79.4789 84.8045C79.6951 84.1274 79.8502 83.4292 80.0147 82.691C80.0476 82.5382 80.0829 82.383 80.1181 82.2279H61.2213L61.2236 82.2255ZM5.53424 30.1763C5.53424 30.3127 5.53659 30.4467 5.53424 30.5807V84.4754C5.53424 86.1774 5.8374 86.4783 7.5553 86.4783H58.6315C58.5822 86.2691 58.5328 86.0622 58.4835 85.8553C58.1968 84.6587 57.9265 83.5279 57.6163 82.3901C57.5881 82.3125 57.4048 82.1479 57.3037 82.1268C54.714 81.5602 53.3415 79.884 53.3321 77.2792C53.3298 76.3788 53.3274 75.4784 53.3321 74.578C53.3462 71.6605 55.2263 69.7657 58.1192 69.7539C59.165 69.7492 60.2108 69.7492 61.2895 69.7539H62.3564C62.3564 69.4859 62.3587 69.2203 62.3611 68.957C62.3681 68.0237 62.3728 67.1397 62.347 66.2534C62.307 64.9322 62.7723 63.9237 63.7688 63.1714C63.8087 63.1079 63.8463 62.9292 63.8299 62.8376C63.5949 61.6127 63.3246 60.3691 63.0614 59.1678L62.8828 58.345C62.629 57.1766 63.2001 56.5771 63.7241 56.2809C64.2153 56.0034 64.7487 55.9682 65.2258 56.1774C65.7663 56.4149 66.1658 56.9462 66.3256 57.6326C66.6123 58.8669 66.8732 60.0752 67.1481 61.3588C67.2374 61.7679 67.3244 62.1793 67.4137 62.5931H73.9351C74.1208 61.7303 74.3158 60.8581 74.5038 60.0141C74.9997 57.7972 75.512 55.5074 75.8786 53.2341C76.2922 50.6504 74.2735 48.0715 71.4652 47.6036C68.866 47.1711 66.2175 48.9201 65.5619 51.5062C65.5548 51.5344 65.5525 51.5626 65.5478 51.5908C65.5407 51.6472 65.5313 51.7013 65.5196 51.7553C65.2258 53.0013 64.4103 53.6502 63.3904 53.4457C62.911 53.3493 62.5279 53.1001 62.2812 52.7192C61.9851 52.2631 61.9099 51.659 62.0626 50.9725C62.6055 48.5487 63.9356 46.6633 66.0201 45.3632C66.3162 45.1775 66.3844 45.0599 66.3844 44.7261C66.375 35.5905 66.375 26.4548 66.375 17.3192V5.13439C66.375 3.95424 65.9496 3.53577 64.7534 3.53577C44.228 3.53577 27.6977 3.53342 7.17224 3.53577C5.93611 3.53577 5.53659 3.94248 5.53659 5.20727C5.53424 8.41157 5.53659 18.2525 5.53659 24.8209C5.54834 24.9973 5.54364 25.1689 5.53894 25.3358C5.53894 25.4063 5.53659 25.4792 5.53659 25.5497V30.1763H5.53424ZM65.9167 66.244V69.6881H75.3781V66.244H65.9167ZM56.8619 76.7644V77.0324C56.8643 78.3724 57.1228 78.6263 58.4882 78.6263C62.9909 78.6263 67.4913 78.6263 71.994 78.6263H76.9056C77.5119 78.6263 78.1182 78.6263 78.7246 78.6263C80.1769 78.6263 81.6809 78.631 83.1568 78.6192C84.1085 78.6122 84.4493 78.2689 84.4611 77.3145C84.4681 76.7197 84.4658 76.1273 84.4634 75.5325V74.886C84.4611 73.5013 84.2378 73.2756 82.8818 73.2733C79.8784 73.2709 76.8774 73.2733 73.874 73.2733H67.1623C64.2059 73.2733 61.2519 73.2733 58.2955 73.2756C57.1604 73.2756 56.8643 73.5671 56.8572 74.6885C56.8525 75.3797 56.8549 76.0708 56.8572 76.762L56.8619 76.7644ZM10.9135 11.7734C11.1415 12.6291 11.816 13.0993 12.8124 13.0993C14.9486 13.0993 17.0871 13.0993 19.2234 13.0993H23.6391C24.4358 13.0993 23.9 13.1016 24.6966 13.1016C25.9163 13.1016 27.136 13.1016 28.3557 13.0946C29.5495 13.0899 30.3274 12.4011 30.3368 11.3385C30.3415 10.8471 30.1793 10.4216 29.8691 10.1089C29.519 9.75629 29.0043 9.56822 28.3792 9.56822C22.7813 9.56116 18.4361 9.56351 12.8382 9.57057C12.427 9.57057 12.0768 9.64109 11.7972 9.7798C11.0169 10.17 10.6856 10.9129 10.9135 11.771V11.7734ZM10.8524 35.1603C10.8994 32.5155 13.0098 30.3033 15.6536 30.1246C15.8158 30.1128 15.9732 30.1152 16.126 30.1152H24.84C36.0145 30.1152 44.5218 30.1152 55.6963 30.1152C57.2544 30.1152 58.6574 30.6841 59.6444 31.7185C60.6126 32.7318 61.1108 34.1165 61.045 35.621C60.9346 38.1741 58.8642 40.2923 56.3332 40.4404C55.7833 40.4733 55.2286 40.4804 54.6811 40.4804C54.4155 40.4804 54.15 40.4804 53.8891 40.4757C53.6212 40.4757 53.3603 40.4733 53.0877 40.471C47.7742 40.471 42.4584 40.471 37.1426 40.471H33.9535C31.8596 40.471 32.4283 40.471 30.3344 40.471C25.2677 40.471 21.3596 40.4757 16.2059 40.4639C14.6572 40.4616 13.2636 39.9044 12.2789 38.9006C11.3318 37.932 10.8219 36.6037 10.8477 35.1579L10.8524 35.1603ZM14.3916 35.4165C14.4668 36.733 15.6442 36.9328 16.3422 36.9328C21.423 36.9375 25.1714 36.9375 30.2522 36.9352H36.9381C42.9966 36.9352 49.0597 36.9352 55.1229 36.9352H55.285C55.5459 36.9352 55.795 36.9352 56.0371 36.9164C56.8126 36.8482 57.4048 36.3051 57.5059 35.5646C57.6234 34.7183 57.1862 33.9966 56.4154 33.7662C56.0394 33.6557 55.5976 33.6463 55.1769 33.6439C41.4385 33.6369 31.6975 33.6392 17.9567 33.6392H16.5325C16.4644 33.6392 16.3962 33.6369 16.3304 33.6369C16.1636 33.6369 16.0014 33.6416 15.8487 33.6604C14.9392 33.7779 14.3399 34.4996 14.394 35.4142L14.3916 35.4165ZM34.2849 13.0758C34.8701 13.0993 35.4693 13.1134 36.078 13.1134C36.6867 13.1134 37.2695 13.1016 37.8758 13.0781C38.8346 13.0405 39.5796 12.2647 39.5702 11.3102C39.5608 10.3464 38.8041 9.61523 37.7677 9.57292C37.3729 9.55646 36.9757 9.56116 36.5927 9.56586C36.4282 9.56586 36.2636 9.56822 36.1015 9.57057C35.9205 9.57057 35.7396 9.57057 35.5586 9.56586C35.1638 9.56116 34.7526 9.55646 34.3436 9.57762C33.3684 9.62699 32.6446 10.344 32.6234 11.2844C32.5999 12.2671 33.3143 13.0382 34.2825 13.0781L34.2849 13.0758Z" fill="white"/></svg>        </span>
        <span class="ctt-service-card-title">Regulatory Due Diligence</span>
      </div>
      <div class="ctt-service-card-body">
        <ul class="ctt-service-list">
          <li><b>Due Diligence:</b> Comprehensive regulatory due diligence assessments for mergers, acquisitions, and portfolio expansion.</li>
          <li><b>Regulatory Audits:</b> Conducting internal and external audits to assess compliance with regulatory standards and guidelines.</li>
        </ul>
      </div>
    </div>
    <!-- Card 4 -->
    <div class="ctt-service-card">
      <div class="ctt-service-card-header">
        <span class="ctt-service-icon">
        <img src="https://krystelis.com/wp-content/uploads/2024/09/Labeling-and-promotional-review.png"
         width="90" height="90"
         alt="Labeeling Icon"
         style="display: block;" />        </span>
        <span class="ctt-service-card-title">Labeling and Promotional Review</span>
      </div>
      <div class="ctt-service-card-body">
        <ul class="ctt-service-list">
          <li><b>Labeling Development and Review:</b> Creation and review of product labels, ensuring compliance with regulatory requirements and accurate representation of product information.</li>
          <li><b>Promotional Materials Review:</b> Evaluation of promotional materials for compliance with regulatory standards and guidelines, including advertising and marketing claims.</li>
        </ul>
      </div>
    </div>
    <!-- Card 5 -->
    <div class="ctt-service-card">
      <div class="ctt-service-card-header">
        <span class="ctt-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="93" viewBox="0 0 90 93" fill="none"><path d="M71.4777 0C72.8839 0.376971 74.2632 0.780695 75.4174 1.75109C77.0877 3.1544 78.0121 4.91765 78.2102 7.06517C78.2591 7.60509 78.2566 8.1523 78.2566 8.69709C78.2566 31.2132 78.2566 53.7318 78.2566 76.2479V77.5053H84.8839C85.5002 77.5053 85.999 78.0014 85.999 78.6143C85.999 81.0196 86.0039 83.3593 85.9917 85.6965C85.9893 86.1318 85.8914 86.8298 85.7716 87.2481C84.7787 90.7114 82.0911 92.8054 78.418 92.9854C78.1759 92.9976 77.9314 92.9951 77.6868 92.9951C60.3997 92.9951 43.1101 92.9903 25.823 93C23.0767 93 20.7584 92.1293 19.049 89.9113C17.8018 88.2939 17.4692 86.4188 17.4692 84.444C17.4717 63.4698 17.4692 42.498 17.4692 21.5238V20.1765H6.16131C5.54994 20.1765 5.0535 19.6876 5.04617 19.082C5.01927 16.7059 4.98503 11.1 5.00704 8.13285C5.03394 4.83982 6.37651 2.27642 9.42847 0.792855C10.2061 0.413452 11.0914 0.2578 11.9302 0C31.7802 0 51.6302 0 71.4802 0H71.4777ZM75.1435 77.4931C75.1435 77.1234 75.1435 76.8851 75.1435 76.6443C75.1435 53.7901 75.1435 30.936 75.1386 8.08177C75.1386 7.62941 75.124 7.16732 75.0286 6.72954C74.515 4.41421 72.7078 3.09603 70.0447 3.0936C53.3616 3.08873 36.681 3.0936 19.9979 3.0936C19.7362 3.0936 19.4745 3.11548 19.005 3.13737C20.9809 5.68862 20.5897 8.55846 20.5897 11.3505C20.5994 35.6274 20.5946 59.9044 20.597 84.1813C20.597 84.6945 20.6141 85.2101 20.663 85.7208C20.9027 88.1845 22.8713 89.9307 25.3657 89.8918C27.7623 89.8529 29.7187 87.934 29.8654 85.5554C30.0072 83.245 29.9363 80.9199 29.9632 78.6021V78.5924C29.9705 77.9844 30.4645 77.4931 31.0783 77.4931H75.1435ZM33.1228 80.6499C32.8978 83.8068 33.7048 87.022 31.4965 89.9015C32.0296 89.9015 32.35 89.9015 32.6704 89.9015C47.6416 89.9015 62.6129 89.9015 77.5817 89.9015C77.9778 89.9015 78.3765 89.9113 78.7653 89.8578C80.7168 89.5927 82.2721 88.6174 82.6487 86.679C83.0302 84.7261 82.9495 82.6856 83.0718 80.6499H33.1228ZM17.4105 17.0172C17.4105 13.6172 17.4105 10.3266 17.4056 7.03355C17.4056 6.85601 17.3445 6.67847 17.298 6.50579C16.6916 4.31206 14.6765 2.91849 12.4193 3.12278C10.0203 3.34167 8.26442 5.02466 8.1666 7.53213C8.04678 10.6403 8.14459 13.7558 8.15438 16.8689C8.15438 16.9151 8.22285 16.9613 8.26687 17.0172H17.4105ZM47.1085 32.8476C40.2318 28.7057 36.3606 22.8177 36.2065 14.7213C36.1796 13.3302 36.2285 11.9366 36.1894 10.5455C36.1747 9.97636 36.3312 9.69424 36.9108 9.5094C40.1242 8.49279 43.3204 7.41782 46.5314 6.39635C46.8762 6.28691 47.3164 6.28691 47.6612 6.39635C50.8721 7.41782 54.0781 8.46361 57.2695 9.54102C57.5605 9.6383 57.9713 10.025 57.9616 10.2658C57.8515 13.2207 57.9811 16.2316 57.4456 19.1161C56.438 24.5445 53.2785 28.6912 48.7152 31.792C48.1894 32.1496 47.6514 32.4925 47.1085 32.85V32.8476ZM54.8754 14.5195C54.8754 13.9455 54.8338 13.3667 54.8876 12.7951C54.9487 12.1482 54.7213 11.8393 54.0781 11.6375C51.9652 10.9735 49.8817 10.2098 47.7688 9.54345C47.3482 9.40969 46.8126 9.42914 46.3871 9.56047C44.8195 10.0372 43.274 10.5892 41.7211 11.1146C39.2976 11.9317 39.2878 11.9317 39.3196 14.4684C39.393 20.3808 41.6746 25.1841 46.5411 28.7082C47.0571 29.0827 47.3579 29.0779 47.8642 28.6936C52.5644 25.116 54.8362 20.3662 54.8778 14.5195H54.8754ZM71.9815 63.5354V63.5233C71.9815 62.7037 71.3139 62.0422 70.4922 62.0422H34.6219C33.7977 62.0422 33.1326 62.7061 33.1326 63.5233V63.5354C33.1326 64.355 33.8002 65.0166 34.6219 65.0166H70.4922C71.3163 65.0166 71.9815 64.3526 71.9815 63.5354ZM23.7884 51.127V51.1416C23.7884 51.9612 24.456 52.6227 25.2777 52.6227H54.9267C55.7508 52.6227 56.416 51.9588 56.416 51.1416V51.127C56.416 50.3074 55.7484 49.6459 54.9267 49.6459H25.2777C24.4535 49.6459 23.7884 50.3098 23.7884 51.127ZM40.9043 44.9544V44.9714C40.9043 45.791 41.5719 46.4526 42.3936 46.4526H70.4995C71.3237 46.4526 71.9889 45.7886 71.9889 44.9714V44.9544C71.9889 44.1348 71.3212 43.4733 70.4995 43.4733H42.3936C41.5695 43.4733 40.9043 44.1372 40.9043 44.9544ZM71.9546 57.3385V57.3166C71.9546 56.497 71.287 55.8355 70.4653 55.8355H42.4009C41.5768 55.8355 40.9116 56.4995 40.9116 57.3166V57.3385C40.9116 58.1581 41.5793 58.8197 42.4009 58.8197H70.4653C71.2894 58.8197 71.9546 58.1557 71.9546 57.3385ZM48.6051 69.7518C48.6051 68.9322 47.9375 68.2707 47.1158 68.2707H25.2728C24.4486 68.2707 23.7835 68.9346 23.7835 69.7518C23.7835 70.5714 24.4511 71.233 25.2728 71.233H47.1134C47.9375 71.233 48.6027 70.569 48.6027 69.7518H48.6051ZM57.92 37.1961V37.1815C57.92 36.3619 57.2524 35.7004 56.4307 35.7004H37.7398C36.9157 35.7004 36.2505 36.3643 36.2505 37.1815V37.1961C36.2505 38.0157 36.9182 38.6772 37.7398 38.6772H56.4307C57.2548 38.6772 57.92 38.0133 57.92 37.1961ZM71.9864 69.7518V69.747C71.9864 68.9273 71.3188 68.2658 70.4971 68.2658H53.2833C52.4592 68.2658 51.794 68.9298 51.794 69.747V69.7518C51.794 70.5714 52.4617 71.233 53.2833 71.233H70.4971C71.3212 71.233 71.9864 70.569 71.9864 69.7518ZM37.6665 44.9666V44.9544C37.6665 44.1348 36.9989 43.4733 36.1772 43.4733H25.2654C24.4413 43.4733 23.7761 44.1372 23.7761 44.9544V44.9666C23.7761 45.7862 24.4437 46.4477 25.2654 46.4477H36.1772C37.0013 46.4477 37.6665 45.7838 37.6665 44.9666ZM36.187 55.8428H25.2899C24.4658 55.8428 23.8006 56.5068 23.8006 57.3239V57.3628C23.8006 58.1825 24.4682 58.844 25.2899 58.844H36.187C37.0111 58.844 37.6763 58.18 37.6763 57.3628V57.3239C37.6763 56.5043 37.0086 55.8428 36.187 55.8428ZM59.6612 51.1367V51.1586C59.6612 51.9782 60.3288 52.6398 61.1505 52.6398H70.4971C71.3212 52.6398 71.9864 51.9758 71.9864 51.1586V51.1367C71.9864 50.3171 71.3188 49.6556 70.4971 49.6556H61.1505C60.3263 49.6556 59.6612 50.3196 59.6612 51.1367ZM24.2188 62.0105C24.0525 62.013 23.759 62.3097 23.7492 62.4824C23.7297 62.8326 23.7223 63.1828 23.7199 63.5354C23.7174 64.3526 24.3875 65.0166 25.2092 65.0166H28.4323C29.2565 65.0166 29.9216 64.3526 29.9216 63.5354V63.4722C29.9216 62.655 29.254 61.9911 28.4323 61.9911C27.0066 61.9911 25.6102 61.9911 24.2163 62.013L24.2188 62.0105ZM46.3675 22.808L46.3895 22.8299C47.0376 23.5303 48.1723 23.4841 48.7421 22.7204C50.2167 20.7431 51.0946 18.5835 51.2854 16.1368C51.3563 15.215 50.5811 14.4489 49.6542 14.5073L49.5833 14.5122C48.8252 14.5608 48.2261 15.1567 48.1527 15.9106C47.9693 17.7639 47.3408 19.4201 46.2477 20.8891C45.8148 21.4703 45.876 22.2802 46.3675 22.8104V22.808Z" fill="white"/></svg>        </span>
        <span class="ctt-service-card-title">Regulatory Intelligence and Training</span>
      </div>
      <div class="ctt-service-card-body">
        <ul class="ctt-service-list">
          <li><b>Regulatory Intelligence:</b> Monitoring and analysing regulatory developments, providing insights and updates on changes in regulatory requirements and guidelines.</li>
          <li><b>Regulatory Training:</b> Conducting training programs and workshops to educate clients on regulatory requirements, submission processes, and compliance best practices.</li>
        </ul>
      </div>
    </div>
  </div>
</section>

<!-- Advantages of Using -->
<section class="advantages-of-using">
  <div class="advantages-of-using-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="advance-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="advance-bg-corner-img" />
  </div>
</section>

<section class="advantages-of-using-section">
  <h2 class="advantages-title">
    Advantages of Using Krystelis<br>Regulatory Services
  </h2>
  <div class="advantages-cards">
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Expertise</div>
      <div class="advantage-card-desc">
        Our team of seasoned regulatory experts brings extensive experience and in-depth knowledge of global regulatory requirements.
      </div>
    </div>
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Efficiency</div>
      <div class="advantage-card-desc">
        We streamline the regulatory process, reducing time to market and ensuring timely approvals.
      </div>
    </div>
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Customisation</div>
      <div class="advantage-card-desc">
        Tailored regulatory strategies and solutions to meet the specific needs of each client and product.
      </div>
    </div>
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Compliance</div>
      <div class="advantage-card-desc">
        Rigorous adherence to regulatory standards and guidelines, ensuring compliance and minimising risks.
      </div>
    </div>
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Support</div>
      <div class="advantage-card-desc">
        Ongoing regulatory support throughout the product lifecycle, from development to post-market activities.
      </div>
    </div>
    <div class="advantage-card">
      <div class="advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="advantage-card-title">Global Reach</div>
      <div class="advantage-card-desc">
        Expertise in navigating regulatory requirements in multiple regions, facilitating global market entry and expansion.
      </div>
    </div>
  </div>
  <div class="advantages-footer">
    Partnering with Krystelis for regulatory services ensures that your pharmaceutical products meet all necessary regulatory requirements, enabling a smoother path to market and sustained compliance.
  </div>
</section>

<!-- Get In Touch CTA Section Start -->
<section class="regulatory-get-in-touch-cta">
  <div class="regulatory-get-in-touch-bg">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Orange-1024x1024.png" alt="Decorative Orange Triangle" class="regulatory-cta-triangle regulatory-cta-triangle-left" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Blue-1024x1024.png" alt="Decorative Blue Triangle" class="regulatory-cta-triangle regulatory-cta-triangle-right" />
    <div class="regulatory-get-in-touch-content">
      <div class="regulatory-get-in-touch-heading">INTERESTED IN LEARNING MORE? </div>
      <div class="regulatory-get-in-touch-title">Get In Touch</div>
      <a href="http://localhost/wordpress/contact-us/" class="regulatory-get-in-touch-btn">CONTACT US</a>
    </div>
  </div>
</section>

<style>
.regulatory-hero-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #fff;
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.regulatory-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  max-width: 100%;
  margin: 0;
  padding: 0;
  gap: 0;
}

.regulatory-hero-text {
  padding: 40px 60px 60px 40px;
  background: #fff;
  /* display: flex; */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}

@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.regulatory-hero-subtitle {
  font-family: "Maven Pro", sans-serif;
  font-size: 1.75rem;
  font-weight: 500;
  color: #FF6A18;
  margin: 0 0 15px 0;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  display: inline-block;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.regulatory-hero-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
}

.regulatory-hero-description {
  font-family: Georgia, serif;
  font-size: 20px;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.regulatory-hero-image {
  position: relative;
  background: #ffffff;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  /* overflow: hidden; */
  padding: 20px;
  margin-top: 7rem;
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.regulatory-hero-image img {
  width: 100%;
  border-radius: 20px;
  display: block;
  margin-right: 3rem;
  object-fit: cover;
  margin-bottom: 40px;
}

/* Responsive styles */
@media (max-width: 999px) {
  .regulatory-hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .regulatory-hero-image img {
    margin: 0 auto;
    display: block;
    border-radius: 12px;
    max-width: 100%;
    height: auto;
  }
}
@media (max-width: 999px) {
  .regulatory-hero-content {
    display: flex;
    flex-direction: column;
    min-height: unset;
    width: 100%;
  }
  .regulatory-hero-text,
  .regulatory-hero-image {
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  .regulatory-hero-image {
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .regulatory-hero-text {
    order: 1;
    padding: 32px 16px 32px 16px;
    align-items: left;
    text-align: left;
  }
}

@media (max-width: 600px) {
  .regulatory-hero-text {
    padding: 18px 4vw 18px 4vw;
  }
  .regulatory-hero-subtitle {
    font-size: 1.25rem;
  }
  .regulatory-hero-title {
    font-size: 2rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .regulatory-hero-description {
    font-size: 1rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .regulatory-hero-image img {
    margin-right: 0;
    border-radius: 12px;
  }
}


.ctt-services-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
  justify-content: center;
  justify-items: center;
}
.ctt-service-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  flex: 1 1 420px;
  min-width: 380px;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  margin: 0 0.5rem;
  opacity: 0;
  transform: translateY(-40px);
  /* No animation by default, will be added by JS */
}

.ctt-service-card.ctt-card-animate {
  animation: cttCardRevealDown 0.8s cubic-bezier(0.23, 1, 0.32, 1) both;
  /* The final shadow is the same as your normal card shadow */
}

@keyframes cttCardRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
    box-shadow: 0 16px 40px rgba(65, 164, 255, 0.25), 0 0 0 rgba(255,255,255,0);
  }
  80% {
    box-shadow: 0 8px 32px rgba(65, 164, 255, 0.18), 0 0 16px 4px rgba(65, 164, 255, 0.15);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  }
}
.ctt-service-card-header {
  background: linear-gradient(180deg, #1A9CF7 0%, #0072DA 100%);
  border-radius: 18px 18px 0 0;
  padding: 1.5rem 2rem 1.5rem 2rem;
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
.ctt-service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
}
.ctt-service-card-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
}
.ctt-service-card-body {
  padding: 2rem 2rem 2.5rem 2rem;
}
.ctt-service-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.ctt-service-list li {
  position: relative;
  padding-left: 1.8em;
  margin-bottom: 1.1em;
  font-family: Georgia, serif;
  font-size: 1.15rem;
  color: #525252;
  line-height: 1.6;
}
.ctt-service-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3em;
  width: 1.1em;
  height: 1.1em;
  background-image: url('data:image/svg+xml;utf8,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z" fill="%23FF9459"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left center;
  display: inline-block;
}

/* Tablet: 1 column, smaller padding */
@media (max-width: 900px) {
  .ctt-services-cards {
    grid-template-columns: 1fr;
    gap: 1.2rem;
    margin-left: 10px;
    margin-right: 10px;
  }
  .ctt-service-card {
    max-width: 98vw;
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(0, 114, 218, 0.10);
  }
  .ctt-service-card-header {
    padding: 1rem 1.2rem;
    border-radius: 14px 14px 0 0;
  }
  .ctt-service-card-title {
    font-size: 1.2rem;
  }
  .ctt-service-card-body {
    padding: 1.2rem 1.2rem 1.5rem 1.2rem;
  }
  .ctt-service-list li {
    font-size: 1rem;
    margin-bottom: 0.7em;
    padding-left: 1.3em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 600px) {
  .regulatory-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .ctt-services-cards {
    gap: 1rem;
  }
  .ctt-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .ctt-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
  }
  .ctt-service-card-title {
    font-size: 1rem;
  }
  .ctt-service-card-body {
    padding: 0.7rem 0.7rem 1rem 0.7rem;
  }
  .ctt-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 480px) {
  .regulatory-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .ctt-services-cards {
    gap: 1rem;
  }
  .ctt-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .ctt-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
    margin-left: 40px;
    margin-right: 40px;
  }
  .ctt-service-card-title {
    font-size: 1rem;
  }
  .ctt-service-card-body {
    padding: 0.7rem 2.7rem 3rem 2.7rem;
  }
  .ctt-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}


.advantages-of-using {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 260px;
}
.advantages-of-using-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transform: translateY(-60px);
  animation: advanceBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes advanceBgTopDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.advance-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
}
.advance-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  animation: advanceBgCornerRightToLeft 2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes advanceBgCornerRightToLeft {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Tablet styles (768px and below) */
@media screen and (max-width: 768px) {
  .advantages-of-using {
    min-height: 200px;
    padding: 0 0 32px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 250px;
  }
  
  .advance-bg-corner-img {
    top: 30px;
    right: 12vw;
    width: 140px;
  }
}

/* Small tablet / Large mobile (600px and below) */
@media screen and (max-width: 600px) {
  .advantages-of-using {
    min-height: 180px;
    padding: 0 0 24px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 200px;
  }
  
  .advance-bg-corner-img {
    top: 20px;
    right: 8vw;
    width: 120px;
  }
}

/* Mobile styles (480px and below) */
@media screen and (max-width: 480px) {
  .advantages-of-using {
    min-height: 160px;
    padding: 0 0 20px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 160px;
  }
  
  .advance-bg-corner-img {
    top: 10px;
        right: 10vw;
        width: 100px;
  }
}

/* Small mobile (375px and below) */
@media screen and (max-width: 375px) {
  .advantages-of-using {
    min-height: 140px;
    padding: 0 0 16px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 140px;
  }
  
  .advance-bg-corner-img {
    top: 8px;
        right: 11vw;
        width: 70px
  }
}

/* Extra small mobile (320px and below) */
@media screen and (max-width: 320px) {
  .advantages-of-using {
    min-height: 120px;
    padding: 0 0 12px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 120px;
  }
  
  .advance-bg-corner-img {
    top: 20px;
    right: 2vw;
    width: 70px;
  }
}

/* Large screens (1200px and above) */
@media screen and (min-width: 1200px) {
  .advantages-of-using {
    min-height: 300px;
    padding: 0 0 60px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 380px;
  }
  
  .advance-bg-corner-img {
    top: 70px;
    right: 15vw;
    width: 220px;
  }
}

Extra large screens (1600px and above)
@media screen and (min-width: 1600px) {
  .advantages-of-using {
    min-height: 350px;
    padding: 0 0 80px 0;
  }
  
  .advantages-of-using-bg-top {
    height: 450px;
  }
  
  .advance-bg-corner-img {
    top: 120px;
    right: 12vw;
    width: 260px;
  }
}

.advantages-of-using-section {
  max-width: 1400px;
  margin: 0 auto 40px auto;
  padding: 0 16px;
  text-align: center;
}

.advantages-title {
  text-align: center;
  font-family: "Maven Pro", sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: #0072DA;
  margin-bottom: 2.5rem;
  margin-bottom: 50px;
  position: relative;
  opacity: 0;
  transform: translateY(-40px);
  animation: advantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes advantagesTitleRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.advantages-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    /* max-width: 1400px; */
    margin: 0 auto;
    /* padding: 0 40px; */
    align-items: stretch;
}

.advantage-card {
    background: #fff;
    border-radius: 24px;
    border: 1px solid #eaeaea;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 36px 36px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: center;
    position: relative;
    height: 100%;
    box-sizing: border-box;
}

.advantage-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);

  }

.advantage-icon {
    position: absolute;
    top: -32px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.advantage-card-title {
  color: #0072DA;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 36px;
  margin-bottom: 12px;
}

.advantage-card-desc {
    /* margin-top: 32px; */
    font-size: 1.35rem;
    color: #525252;
    font-family: Georgia, serif;
    line-height: 1.5;
}

.advantages-footer {
  color: #FF6A18;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 32px;
  letter-spacing: 1px;
}

/* Responsive */
@media (max-width: 1100px) {
  .advantages-cards {
    grid-template-columns: 1fr 1fr;
    gap: 28px 20px;
  }
}
@media (max-width: 700px) {
  .advantages-title {
    font-size: 1.5rem;
    margin-bottom: 40px;
  }
  .advantages-cards {
    grid-template-columns: 1fr;
    gap: 60px 0;
  }
  .advantage-card {
    padding: 28px 10px 24px 10px;
    min-height: 120px;
  }
  .advantage-card-title {
    font-size: 1.1rem;
    margin-top: 28px;
  }
  .advantages-footer {
    font-size: 1rem;
    margin-top: 18px;
  }
}

.regulatory-get-in-touch-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.regulatory-get-in-touch-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
}
.regulatory-get-in-touch-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.regulatory-get-in-touch-heading {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 12px;
}
.regulatory-get-in-touch-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.regulatory-get-in-touch-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.regulatory-get-in-touch-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.regulatory-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.regulatory-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.regulatory-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .regulatory-get-in-touch-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .regulatory-get-in-touch-title {
    font-size: 2.2rem;
  }
  .regulatory-get-in-touch-heading {
    font-size: 1.2rem;
  }
  .regulatory-cta-triangle {
    width: 100px;
  }
  .regulatory-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .regulatory-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .regulatory-get-in-touch-bg {
    min-height: 180px;
    padding: 0;
  }
  .regulatory-get-in-touch-content {
    padding: 24px 4vw 24px 4vw;
  }
  .regulatory-get-in-touch-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .regulatory-get-in-touch-heading {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .regulatory-get-in-touch-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .regulatory-cta-triangle {
    width: 48px;
  }
  .regulatory-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .regulatory-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}
</style>


<script>
document.addEventListener('DOMContentLoaded', function () {
  function moveRegulatoryHeroImageResponsive() {
    var heroText = document.querySelector('.regulatory-hero-text');
    var heroImage = document.querySelector('.regulatory-hero-image');
    var heroSubtitle = document.querySelector('.regulatory-hero-subtitle');
    var heroContent = document.querySelector('.regulatory-hero-content');
    
    if (window.innerWidth <= 999) {
      // Mobile: Move image after subtitle
      if (heroText && heroImage && heroSubtitle) {
        var nextSibling = heroSubtitle.nextElementSibling;
        if (nextSibling !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroSubtitle.parentNode.insertBefore(heroImage, heroSubtitle.nextSibling);
        }
      }
    } else {
      // Desktop: Move image back to original position
      if (heroContent && heroImage) {
        var lastChild = heroContent.lastElementChild;
        if (lastChild !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroContent.appendChild(heroImage);
        }
      }
    }
  }
  moveRegulatoryHeroImageResponsive();
  window.addEventListener('resize', moveRegulatoryHeroImageResponsive);
});


document.addEventListener('DOMContentLoaded', function () {
  var subtitle = document.querySelector('.regulatory-hero-subtitle');
  if (!subtitle) return;

  // Helper to trigger animation
  function triggerAnimation() {
    subtitle.style.animation = 'none';
    // Force reflow
    void subtitle.offsetWidth;
    subtitle.style.animation = 'revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation (in case not already triggered)
  triggerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(subtitle);
});


document.addEventListener('DOMContentLoaded', function () {
  var heroImage = document.querySelector('.regulatory-hero-image');
  if (!heroImage) return;

  function triggerZoomAnimation() {
    heroImage.style.animation = 'none';
    void heroImage.offsetWidth; // Force reflow
    heroImage.style.animation = 'zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both';
  }

  // Initial animation
  triggerZoomAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerZoomAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(heroImage);
});

document.addEventListener('DOMContentLoaded', function () {
  var cards = document.querySelectorAll('.ctt-service-card');
  if (!cards.length) return;

  var observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        // Staggered animation: delay based on card index
        var index = Array.from(cards).indexOf(entry.target);
        setTimeout(function() {
          entry.target.classList.add('ctt-card-animate');
        }, index * 200); // 200ms delay between each card
        observer.unobserve(entry.target); // Animate only once
      }
    });
  }, { threshold: 0.3 });

  cards.forEach(function(card) {
    observer.observe(card);
  });
});

document.addEventListener('DOMContentLoaded', function () {
  var bgImg = document.querySelector('.advance-bg-top-img');
  if (!bgImg) return;

  function triggerBgAnimation() {
    bgImg.style.animation = 'none';
    void bgImg.offsetWidth; // Force reflow
    bgImg.style.animation = 'advanceBgTopDown 5s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerBgAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerBgAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(bgImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var cornerImg = document.querySelector('.advance-bg-corner-img');
  if (!cornerImg) return;

  function triggerCornerAnimation() {
    cornerImg.style.animation = 'none';
    void cornerImg.offsetWidth; // Force reflow
    cornerImg.style.animation = 'advanceBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerCornerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerCornerAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(cornerImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var advTitle = document.querySelector('.advantages-title');
  if (!advTitle) return;

  function triggerAdvTitleAnimation() {
    advTitle.style.animation = 'none';
    void advTitle.offsetWidth;
    advTitle.style.animation = 'advantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerAdvTitleAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAdvTitleAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(advTitle);
});
</script>

<?php get_footer(); ?> 