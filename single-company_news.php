<?php get_header(); ?>

<div class="company-news-single-container">
    <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
        <div class="company-news-meta">
            <span class="company-news-date"><?php echo get_the_date('F j, Y'); ?></span>
            <span class="company-news-sep">|</span>
            <span class="company-news-author">
                BY <a href="#">
                <!-- <?php echo get_author_posts_url(get_the_author_meta('ID')); ?> -->
                    <?php the_author(); ?>
                </a>
            </span>
            <span class="company-news-sep">|</span>
            <span class="company-news-category">
                <a href="#">Company News</a>
                <!-- <?php echo get_post_type_archive_link('company_news'); ?> -->
            </span>
        </div>

        <!-- Heading + Paragraph + Image Row -->
        <div class="company-news-headpara-image-row">
            <div class="company-news-headpara">
                <h1 class="company-news-title"><?php the_title(); ?></h1>
                <?php
                // Get the content and split at the first </p>
                $content = apply_filters('the_content', get_the_content());
                if (preg_match('/<p>(.*?)<\/p>/is', $content, $matches)) {
                    echo '<p class="company-news-paragraph">' . $matches[1] . '</p>';
                    // Remove the first paragraph from content for later use
                    $content = preg_replace('/<p>(.*?)<\/p>/is', '', $content, 1);
                }
                ?>
            </div>
            <?php if (has_post_thumbnail()) : ?>
                <div class="company-news-headpara-image">
                    <?php the_post_thumbnail('large'); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Details Section -->
        <div class="company-news-details-section">
            <?php echo $content; // The rest of the content ?>
        </div>
    <?php endwhile; endif; ?>
</div>

<div class="company-news-tags-row">
    <div class="company-news-tags-label">
        <span>Tags:</span>
        <?php the_tags('<span class="company-news-tag-list">', ', ', '</span>'); ?>
    </div>
    <div class="company-news-tags-share">
        <svg width="24" height="24" fill="none" stroke="#525252" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="company-news-share-icon"><circle cx="18" cy="5" r="3"/><circle cx="6" cy="12" r="3"/><circle cx="18" cy="19" r="3"/><path d="M8.59 13.51l6.83 3.98M15.41 6.51l-6.82 3.98"/></svg>
    </div>
</div>

<style>
.company-news-single-container {
    max-width: 1600px;
    margin: 40px auto;
    padding: 0 20px;
}
.company-news-meta {
    text-align: center;
    color: #bbb;
    font-size: 1rem;
    margin-bottom: 30px;
    letter-spacing: 1px;
}
.company-news-meta a {
    color: #bbb;
    transition: color 0.2s;
}
.company-news-meta a:hover {
    color: #0072DA;
}
.company-news-sep {
    margin: 0 10px;
}

.company-news-headpara-image-row {
    display: flex;
    gap: 40px;
    margin: 40px 80px 40px 80px;
}
.company-news-headpara {
    flex: 0 0 60%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.company-news-title {
    color: #0072DA;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 24px;
}
.company-news-paragraph {
    font-size: 24px;
    color: #525252;
    font-family: 'georgia', serif;
    margin-bottom: 0;
}
.company-news-headpara-image {
    flex: 0 0 40%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.company-news-headpara-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.company-news-details-section {
    margin: 40px 80px 0 80px;
    font-size: 24px;
    color: #525252;
    font-family: 'georgia', serif;
}
.company-news-excerpt {
    font-size: 24.0004px;
    color: #525252;
    margin-bottom: 32px;
    font-family: 'georgia', serif;
}
.company-news-body {
    font-size: 24.0004px;
    color: #525252;
    margin-bottom: 32px;
    font-family: 'georgia', serif;
}
.company-news-image {
    flex: 1;
    border: 2px solid #ffe066;
    background: #ffe066;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 350px;
}
.company-news-image img {
    width: 100%;
    height: auto;
    display: block;
}
.company-news-title-image img {
    width: 60px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-left: 12px;
}

.company-news-tags-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #e5e5e5;
    border-bottom: 1px solid #e5e5e5;
    padding: 32px 0;
    margin: 40px 0 0 0;
}
.company-news-tags-label {
    font-size: 25px;
    color: #525252;
    font-family: 'maven-pro', serif;
    font-weight: 500;
    margin-left: 15rem;
}
.company-news-tag-list a {
    color: #0072DA; /* Blue by default */
    text-decoration: none;
    font-size: 1.5rem;
    margin-left: 8px;
    transition: color 0.2s;
}
.company-news-tag-list a:hover {
    color: orange; /* Orange on hover */
}
.company-news-tags-share {
    font-size: 2rem;
    color: #525252;
    margin-right: 15rem;
}
.company-news-share-icon {
    vertical-align: middle;
}
.company-news-paragraph a,
.company-news-details-section a {
    color: #0072DA;
    text-decoration: none;
    transition: color 0.2s;
}
.company-news-paragraph a:hover,
.company-news-details-section a:hover {
    color: orange;
}
@media (max-width: 900px) {
    .company-news-headpara-image-row {
        flex-direction: column;
        margin: 24px 0;
    }
    .company-news-headpara,
    .company-news-headpara-image,
    .company-news-details-section {
        flex: 1 1 100%;
        margin: 0;
    }
    .company-news-title {
        font-size: 2.2rem;
    }
}
</style>

<?php get_footer(); ?> 