<?php
/**
 * Template part for displaying hero section
 *
 * @package Krystelis_Custom
 */

// Get hero background image from ACF or use default
$hero_bg_image = krystelis_get_field('hero_background_image', get_option('page_on_front'));
$hero_bg_url = $hero_bg_image ? $hero_bg_image['url'] : get_template_directory_uri() . '/assets/images/Krystelis-Home-Main-Banner.png';

// Get hero content from ACF or use defaults
$hero_title = krystelis_get_field('hero_title', get_option('page_on_front'), 'Making<br>clinical research<br>crystal clear');
$hero_subtitle = krystelis_get_field('hero_subtitle', get_option('page_on_front'), 'Great people are at the heart of the most valuable partnerships – our exceptional team stands ready to support you');
$hero_cta_text = krystelis_get_field('hero_cta_text', get_option('page_on_front'), 'READ MORE');
$hero_cta_url = krystelis_get_field('hero_cta_url', get_option('page_on_front'), '#services');

// Get orange shape background
$orange_shape_bg = get_template_directory_uri() . '/assets/images/Bg-Shape-Orange.png';
?>

<section class="hero-section">
    <!-- Full-screen background image -->
    <div class="hero-background">
        <img src="<?php echo esc_url($hero_bg_url); ?>" alt="Krystelis Hero Background" class="hero-bg-image">
        <div class="hero-overlay"></div>
    </div>

    <!-- Orange diagonal design element -->
    <div class="orange-diagonal-shape"></div>

    <!-- Hero content -->
    <div class="hero-container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title"><?php echo wp_kses_post($hero_title); ?></h1>
                <p class="hero-subtitle"><?php echo esc_html($hero_subtitle); ?></p>
                <div class="hero-cta">
                    <a href="http://*************/#services-section" class="btn btn-orange hero-btn">
                        <?php echo esc_html($hero_cta_text); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get content for Why How Who section from ACF or use defaults
$why_title = krystelis_get_field('why_title', get_option('page_on_front'), 'Why we do it?');
$why_content = krystelis_get_field('why_content', get_option('page_on_front'), 'To help our clients improve the lives of patients...');

$how_title = krystelis_get_field('how_title', get_option('page_on_front'), 'How we do it?');
$how_content = krystelis_get_field('how_content', get_option('page_on_front'), '...by providing services recognised for quality, value, and collaboration');

$who_title = krystelis_get_field('who_title', get_option('page_on_front'), 'Who we are');
$who_content = krystelis_get_field('who_content', get_option('page_on_front'), 'A team of life sciences leaders with a passion for customer service. Krystelis is a UK headquartered company with operations in India.');
$who_cta_text = krystelis_get_field('who_cta_text', get_option('page_on_front'), 'READ MORE');
$who_cta_url = krystelis_get_field('who_cta_url', get_option('page_on_front'), 'http://*************/about-us/');

// Get team image
$team_image = get_template_directory_uri() . '/assets/images/why choose us.png';
?>



            <div class="why-how-container">
            <!-- Why/How We Do It Boxes -->
            <div class="why-how-boxes">
                <div class="why-box">
                    <div class="box-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Why-we-do-it-icon.png" alt="Why we do it icon">
                    </div>
                    <div class="box-content">
                        <h3>Why we do it?</h3>
                        <p>To help our clients improve the lives of patients...</p>
                    </div>
                </div>

                <div class="how-box">
                    <div class="box-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/How-we-do-it-icon.png" alt="How we do it icon">
                    </div>
                    <div class="box-content">
                        <h3>How we do it?</h3>
                        <p>...by providing services recognised for quality, value, and collaboration</p>
                    </div>
                </div>
            </div>

        <!-- Bottom Content Row -->
        <div class="content-row">
            <!-- Team Image -->
            <div class="team-image-container">
                <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/about.png'); ?>" alt="Team Working" class="team-image">
                <div class="image-decorations">
                    <div class="decoration-shape shape-1"></div>
                    <div class="decoration-shape shape-2"></div>
                    <div class="decoration-shape shape-3"></div>
                </div>
            </div>

            <!-- Who We Are Content -->
            <div class="who-content">
                <h2 class="who-title"><?php echo esc_html($who_title); ?></h2>
                <p class="who-text"><?php echo esc_html($who_content); ?></p>
                <div class="who-cta">
                    <a href="<?php echo esc_url($who_cta_url); ?>" class="btn btn-orange who-btn">
                        <?php echo esc_html($who_cta_text); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get content for Why Choose Us section from ACF or use defaults
$why_choose_title = krystelis_get_field('why_choose_title', get_option('page_on_front'), 'Why choose us');
$why_choose_content = krystelis_get_field('why_choose_content', get_option('page_on_front'), 'Our clients continuously recognise the quality, value, and cost-effectiveness of the work we deliver to them. Our flexible, proactive and collaborative approach makes us easy to work with.');
$why_choose_cta_text = krystelis_get_field('why_choose_cta_text', get_option('page_on_front'), 'READ MORE');
$why_choose_cta_url = krystelis_get_field('why_choose_cta_url', get_option('page_on_front'), 'http://*************/about-us/');

// Get team image for why choose us section
$why_choose_image = get_template_directory_uri() . '/assets/images/why choose usss.png';
?>

<section class="why-choose-us-section">
    <div class="why-choose-container">
        <div class="why-choose-content">
            <div class="why-choose-text">
                <h2 class="why-choose-title"><?php echo esc_html($why_choose_title); ?></h2>
                <p class="why-choose-description"><?php echo esc_html($why_choose_content); ?></p>
                <div class="why-choose-cta">
                    <a href="<?php echo esc_url($why_choose_cta_url); ?>" class="btn btn-orange why-choose-btn">
                        <?php echo esc_html($why_choose_cta_text); ?>
                    </a>
                </div>
            </div>
            <div class="why-choose-image-container">
                    <img src="<?php echo esc_url($why_choose_image); ?>" alt="Our Team" class="why-choose-image">
                    <div class="image-decorative-elements">
                        <div class="decorative-element element-1"></div>
                        <div class="decorative-element element-2"></div>
                        <div class="decorative-element element-3"></div>
                        <div class="decorative-element element-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    position: relative;
    min-height: 100vh;
    background: #ffffff;
    overflow: hidden;
    margin-top: -30px;
}

/* Full-screen background image */
.hero-background {
    position: absolute;
    top: 170px;
    left: 0;
    width: 100%;
    height: 100vh;
    /* z-index: 1; */
    opacity: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 2;
}


.hero-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    z-index: 4;
}

.hero-content {
    display: flex;
    align-items: center;
    min-height: 100vh;
    width: 100%;
    padding-top: 120px; /* Space for header */
}

.hero-text {
    flex: 1;
    max-width: 50%;
    padding: 2rem 20px 2rem 20px; /* 20px horizontal margins as per user preference */
    z-index: 5;
    position: relative;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 600;
    margin-bottom: 1rem !important;
    text-align: left;
    margin-left: 20px;
    margin-top: 5rem;
    line-height: 1.1;
    color: #0072DA;
    font-family: "Maven Pro", sans-serif;
    opacity: 1;
}

.hero-subtitle {
    font-size: 24.0024px;
    max-width: 500px;
    color: #525252;
    margin-left: 20px;
    line-height: 1.6;
    font-family: 'Arial', sans-serif;
    opacity: 1;
}

.hero-cta {
    margin-top: 2rem;
    opacity: 1;
}

.btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem ;
    margin-left: 1rem !important;
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem;
    font-family: "Maven Pro", sans-serif !important;
}

.btn-orange,
.hero-btn {
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-orange:hover {
    background-color: #e55a2b;
    color: white;
    text-decoration: none;
}

/* Animation Keyframes */
@keyframes slideDown {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0px);
    opacity: 1;
  }
}

@keyframes slideUpSlow {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0px);
    opacity: 1;
  }
}

/* Initially hide */
.hero-background,
.hero-content {
  opacity: 0;
  transform: translateY(0); /* avoid flash during load */
}

/* Triggered animations */
.animate-bg {
  animation: slideDown 1s ease-out forwards;
}

.animate-content {
  animation: slideUpSlow 1.5s ease-out forwards;
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 100vh;
        position: absolute;
        top: 44px;
        left: 0;
        width: 100%;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: left;
        padding: 2rem 5px; /* Minimal margins on mobile as per user preference */
        order: 2;
    }

    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem !important;
        margin-left: 3rem;
        margin-top: 20rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 2.50rem;
        padding: 0 10px;
        text-align: left;
    }

    .btn {
        padding: 1.2rem 2.5rem;
        font-size: 1.1rem;
        margin-left: 10px;

    }

    .hero-cta {
        margin-top: 1.5rem;
        margin-right: 4rem;
    }
}

/* Tablet styles (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .hero-title {
        margin-bottom: 1rem;
        margin-top: 15rem;
    }
    .hero-subtitle {
        margin-top: 0 !important;
        text-align: left !important;
        margin-left: 15px !important;
    }
}

/* Mobile and tablet portrait (600px - 768px) */
@media (min-width: 600px) and (max-width: 768px) {
    .hero-title {
        margin-bottom: 1rem;
        margin-top: 15rem;
        font-size: 2.2rem;
        margin-left: 15px;
    }
    .hero-subtitle {
        margin-top: 0 !important;
        text-align: left !important;
        margin-left: 15px !important;
        font-size: 1.1rem;
    }
    .hero-background {
        height: 60vh;
        top: 150px;
    }
    .btn {
        margin-left: 15px !important;
    }
}

/* Large mobile phones (480px - 599px) */
@media (min-width: 480px) and (max-width: 599px) {
    .hero-title {
        font-size: 2rem;
        margin-top: 18rem !important;
        margin-left: 15px !important;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-left: 15px !important;
        margin-top: 0 !important;
        text-align: left !important;
        max-width: 80%;
    }

    .hero-background {
        height: 40vh;
        top: 150px;
    }

    .btn {
        padding: 1.1rem 2rem;
        font-size: 1rem;
        margin-left: 15px !important;
    }
}

/* Medium mobile phones (375px - 479px) */
@media (min-width: 375px) and (max-width: 479px) {
    .hero-title {
        font-size: 1.8rem;
        /* margin-top: 18rem !important; */
        margin-left: 12px !important;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.95rem;
        margin-left: 12px !important;
        margin-top: 0 !important;
        text-align: left !important;
    }

    .hero-background {
        height: 50vh;
        top: 140px;
    }

    .btn {
        padding: 1rem 1.8rem;
        font-size: 0.95rem;
        margin-left: 12px !important;
    }

    .hero-text {
        padding: 1.5rem 8px;
    }
}

/* Small mobile phones (320px - 374px) */
@media (min-width: 320px) and (max-width: 374px) {
    .hero-title {
        font-size: 1.5rem;
        margin-top: 15rem !important;
        margin-left: 10px !important;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-left: 10px !important;
        margin-top: 0 !important;
        text-align: left !important;
    }

    .hero-background {
        height: 45vh;
        top: 130px;
    }

    .btn {
        padding: 0.9rem 1.5rem;
        font-size: 0.9rem;
        margin-left: 10px !important;
    }

    .hero-text {
        max-width: 100%;
        padding: 1.5rem 5px;
    }
}

/* Extra small devices (280px - 319px) */
@media (min-width: 280px) and (max-width: 319px) {
    .hero-title {
        font-size: 1.3rem;
        margin-top: 10rem !important;
        margin-left: 8px !important;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.8rem;
        margin-left: 8px !important;
        margin-top: 0 !important;
        text-align: left !important;
    }

    .hero-background {
        height: 40vh;
        top: 120px;
    }

    .btn {
        padding: 0.8rem 1.3rem;
        font-size: 0.8rem;
        margin-left: 8px !important;
    }

    .hero-text {
        max-width: 100%;
        padding: 1rem 3px;
    }
}

/* Very small devices (below 280px) */
@media (max-width: 279px) {
    .hero-title {
        font-size: 1.1rem;
        margin-top: 8rem !important;
        margin-left: 5px !important;
        margin-bottom: 1rem;
    }

    .hero-subtitle {
        font-size: 0.75rem;
        margin-left: 5px !important;
        margin-top: 0 !important;
        text-align: left !important;
    }

    .hero-background {
        height: 35vh;
        top: 110px;
    }

    .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.75rem;
        margin-left: 5px !important;
    }

    .hero-text {
        max-width: 100%;
        padding: 0.8rem 2px;
    }
}

/* Custom media query for 412x914 screen (Samsung Galaxy S20/S21 and similar) */
@media (min-width: 412px) and (max-width: 414px) and (min-height: 900px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 35vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.1rem;
        margin-bottom: 1rem !important;
        margin-left: 15px !important;
        margin-top: 20rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.05rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 15px !important;
        padding: 0 10px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.1rem 2.2rem;
        font-size: 1.05rem;
        margin-left: 15px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 412px) and (height: 914px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.1rem;
        margin-bottom: 1rem !important;
        margin-left: 15px !important;
        margin-top: 18rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.05rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 15px !important;
        padding: 0 10px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.1rem 2.2rem;
        font-size: 1.05rem;
        margin-left: 15px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}

/* Custom media query for 414x896 screen (iPhone 11, XR and similar) */
@media (min-width: 414px) and (max-width: 416px) and (min-height: 890px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 35vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.1rem;
        margin-bottom: 1rem !important;
        margin-left: 15px !important;
        margin-top: 20rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.05rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 15px !important;
        padding: 0 10px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.1rem 2.2rem;
        font-size: 1.05rem;
        margin-left: 15px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
        margin-right: 9rem;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 414px) and (height: 896px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.1rem;
        margin-bottom: 1rem !important;
        margin-left: 15px !important;
        margin-top: 17rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.05rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 15px !important;
        padding: 0 10px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.1rem 2.2rem;
        font-size: 1.05rem;
        margin-left: 15px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}
/* Custom media query for 390x844 screen (iPhone 12, 13, 14 and similar) */
@media (min-width: 390px) and (max-width: 392px) and (min-height: 840px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 40vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.0rem;
        margin-bottom: 1rem !important;
        margin-left: 12px !important;
        margin-top: 20rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.0rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 12px !important;
        padding: 0 8px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.0rem 2.0rem;
        font-size: 1.0rem;
        margin-left: 12px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
        margin-right: 8rem;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 390px) and (height: 844px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.0rem;
        margin-bottom: 1rem !important;
        margin-left: 12px !important;
        margin-top: 15.5rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.0rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 12px !important;
        padding: 0 8px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.0rem 2.0rem;
        font-size: 1.0rem;
        margin-left: 12px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}

/* Custom media query for 430x932 screen (iPhone 14 Pro Max, 15 Pro Max and similar) */
@media (min-width: 430px) and (max-width: 432px) and (min-height: 930px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 35vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem !important;
        margin-left: 18px !important;
        margin-top: 22rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 18px !important;
        padding: 0 12px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.2rem 2.4rem;
        font-size: 1.1rem;
        margin-left: 18px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
        margin-right: 9rem;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 430px) and (height: 932px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem !important;
        margin-left: 18px !important;
        margin-top: 18.5rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 18px !important;
        padding: 0 12px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.2rem 2.4rem;
        font-size: 1.1rem;
        margin-left: 18px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}
/* Custom media query for 360x740 screen (Samsung Galaxy S8/S9 and similar) */
@media (min-width: 360px) and (max-width: 362px) and (min-height: 735px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 1.9rem;
        margin-bottom: 1rem !important;
        margin-left: 10px !important;
        margin-top: 22rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.95rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 10px !important;
        padding: 0 6px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 0.9rem 1.8rem;
        font-size: 0.95rem;
        margin-left: 10px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
        text-align: left !important;
        margin-left: 10px !important;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 360px) and (height: 740px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 35vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 1.9rem;
        margin-bottom: 1rem !important;
        margin-left: 10px !important;
        margin-top: 16rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.95rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 10px !important;
        padding: 0 6px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 0.9rem 1.8rem;
        font-size: 0.95rem;
        margin-left: 10px !important;
    }

    .hero-cta {
        margin-top: 1.5rem;
        margin-right: 5rem;
    }
}
/* Exact media query for 820x1180 screen (iPad Air, iPad Pro 11" and similar tablets) */
@media (width: 820px) and (height: 1180px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 3.5rem;
        margin-bottom: 1.5rem !important;
        margin-left: 40px !important;
        margin-top: 28rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.8rem;
        margin-bottom: 2.5rem;
        max-width: 100%;
        margin-left: 40px !important;
        padding: 0 20px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.8rem 3.6rem;
        font-size: 1.6rem;
        margin-left: 40px !important;
    }

    .hero-cta {
        margin-top: 2rem;
    }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 820px) and (height: 1180px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 65vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 3.5rem;
        margin-bottom: 1.5rem !important;
        margin-left: 40px !important;
        margin-top: 28rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.8rem;
        margin-bottom: 2.5rem;
        max-width: 100%;
        margin-left: 40px !important;
        padding: 0 20px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 1.8rem 3.6rem;
        font-size: 1.6rem;
        margin-left: 40px !important;
    }

    .hero-cta {
        margin-top: 2rem;
    }
}

/* Custom media query for 1024x1366 screen (iPad Pro portrait and similar large tablets) */
@media (min-width: 1024px) and (max-width: 1026px) and (min-height: 1360px) {
    .hero-section { min-height: 100vh; margin-top: 0; }
    .hero-background { width: 100%; height: 40vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 4.5rem; margin-bottom: 2rem !important; margin-left: 60px !important; margin-top: 18rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 2.4rem; margin-bottom: 3rem; max-width: 100%; margin-left: 60px !important; padding: 0 30px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 2.2rem 4.4rem; font-size: 2rem; margin-left: 60px !important; }
    .hero-cta { margin-top: 2rem; }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 1024px) and (height: 1366px) {
    .hero-section { min-height: 100vh; margin-top: 0; }
    .hero-background { width: 100%; height: 65vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; text-align: center; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 4.5rem; margin-bottom: 2rem !important; margin-left: 60px !important; margin-top: 28rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 2.4rem; margin-bottom: 3rem; max-width: 100%; margin-left: 60px !important; padding: 0 30px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 2.2rem 4.4rem; font-size: 2rem; margin-left: 60px !important; }
    .hero-cta { margin-top: 2rem; }
}
/* Exact media query for 912x1368 screen (iPad Pro 12.9" and similar large tablets) */
@media (width: 912px) and (height: 1368px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 32vh;
        position: absolute;
        top: 120px;
        left: 0;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        padding: 2rem 8px;
        order: 2;
    }

    .hero-title {
        font-size: 4.2rem;
        margin-bottom: 1.8rem !important;
        margin-left: 50px !important;
        margin-top: 35rem !important;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 2.2rem;
        margin-bottom: 3rem;
        max-width: 100%;
        margin-left: 50px !important;
        padding: 0 25px;
        text-align: left !important;
        margin-top: 0 !important;
    }

    .btn {
        padding: 2rem 4rem;
        font-size: 1.8rem;
        margin-left: 50px !important;
    }

    .hero-cta {
        margin-top: 2.5rem;
    }
}
/* Custom media query for 344x882 screen (Compact mobile devices) */
@media (min-width: 344px) and (max-width: 346px) and (min-height: 880px) {
    .hero-section { min-height: 100vh; margin-top: 0; }
    .hero-background { width: 100%; height: 35vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; text-align: center; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 1.8rem; margin-bottom: 1rem !important; margin-left: 8px !important; margin-top: 18rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 0.9rem; margin-bottom: 2rem; max-width: 100%; margin-left: 8px !important; padding: 0 5px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 0.85rem 1.7rem; font-size: 0.9rem; margin-left: 8px !important; }
    .hero-cta { margin-top: 1.5rem; margin-right: 5rem; }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 344px) and (height: 882px) {
    .hero-section { min-height: 100vh; margin-top: 0; }
    .hero-background { width: 100%; height: 65vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; text-align: center; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 1.8rem; margin-bottom: 1rem !important; margin-left: 8px !important; margin-top: 16rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 0.9rem; margin-bottom: 2rem; max-width: 100%; margin-left: 8px !important; padding: 0 5px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 0.85rem 1.7rem; font-size: 0.9rem; margin-left: 8px !important; }
    .hero-cta { margin-top: 1.5rem; }
}
/* Custom media query for 853x1280 screen (Large tablets and similar) */
@media (min-width: 853px) and (max-width: 855px) and (min-height: 1275px) {
    .hero-section { margin-top: 0; }
    .hero-background { width: 100%; height: 35vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 3.8rem; margin-bottom: 1.6rem !important; margin-left: 65px !important; margin-top: 20rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 2.0rem; margin-bottom: 2.8rem; max-width: 100%; margin-left: 45px !important; padding: 0 22px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 1.9rem 3.8rem; font-size: 1.7rem; margin-left: 45px !important; }
    .hero-cta { margin-top: 2.2rem; }
}

/* Alternative approach targeting exact device dimensions */
@media (width: 853px) and (height: 1280px) {
    .hero-section { min-height: 100vh; margin-top: 0; }
    .hero-background { width: 100%; height: 65vh; position: absolute; top: 120px; left: 0; z-index: 1; }
    .hero-content { flex-direction: column; min-height: 100vh; padding-top: 80px; }
    .hero-text { max-width: 100%; text-align: center; padding: 2rem 8px; order: 2; }
    .hero-title { font-size: 3.8rem; margin-bottom: 1.6rem !important; margin-left: 45px !important; margin-top: 32rem !important; line-height: 1.2; }
    .hero-subtitle { font-size: 2.0rem; margin-bottom: 2.8rem; max-width: 100%; margin-left: 45px !important; padding: 0 22px; text-align: left !important; margin-top: 0 !important; }
    .btn { padding: 1.9rem 3.8rem; font-size: 1.7rem; margin-left: 45px !important; }
    .hero-cta { margin-top: 2.2rem; }
}
/* @media (max-width: 1024px) {
    .hero-title {
        margin-bottom: 1rem !important;
        margin-top: 15rem;
        font-size: 2.5rem;
    } 
} */


@media (min-width: 1025px) {
    .hero-title {
        text-align: left;
        margin-left: 20px;
        margin-top: 20rem;
    }
    .hero-subtitle {
        text-align: left;
        margin-left: 20px;
        margin-top: 0;
    }
}
/* Ultra-wide screens (2560px and above) */
@media (min-width: 2560px) {
    .hero-title {
        font-size: 4rem;
    }
    .hero-subtitle {
        font-size: 2rem;
        max-width: 900px;
    }
    .hero-text {
        max-width: 60%;
    }
}

/* Super ultra-wide screens (3425px and above) */
@media (min-width: 3425px) {
    .hero-title {
        font-size: 5rem;
    }
    .hero-subtitle {
        font-size: 2.5rem;
        max-width: 1200px;
    }
    .hero-text {
        max-width: 50%;
    }
}

/* Large desktop (1024px - 2559px) */
@media (min-width: 1024px) and (max-width: 2559px) {
    .hero-title {
        font-size: 3.2rem;
    }
    .hero-subtitle {
        font-size: 1.5rem;
        max-width: 700px;
    }
    .hero-text {
        max-width: 50%;
    }
}

/* Tablet landscape (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-title {
        font-size: 2rem;
        margin-top: 20rem !important;
        margin-left: 20px !important;
    }
    .hero-subtitle {
        font-size: 1.1rem;
        margin-left: 20px !important;
        width: 40%; /* or max-width: 40%; based on your layout */
    }
    .hero-text {
        max-width: 100%;
        padding: 2rem 10px;
    }
    .hero-background {
        top: 170px;
    }
}

/* Tablet portrait and large mobile (600px - 767px) */
@media (min-width: 600px) and (max-width: 767px) {
    .hero-title {
        font-size: 1.7rem;
        margin-top: 18rem !important;
        margin-left: 20px !important;
    }
    .hero-subtitle {
        font-size: 1rem;
        margin-left: 20px !important;
        width: 50%; /* or max-width: 50%; based on your layout */
    }
    .hero-text {
        max-width: 100%;
        padding: 2rem 8px;
    }
}

@media (min-width: 844px) and (max-width: 1750px) {
    .hero-subtitle {
        margin-top: 2rem;
        position: relative;
        z-index: 5;
        max-width: 40%;
        margin-left: 15px;
    }
}


/* Why Choose Us Section Styles */
.why-choose-us-section {
    padding: 1rem 0;
    background:rgb(240, 240, 240);
    position: relative;
    overflow: hidden;
}

/* Gentle flowing wave at the top - reversed direction */
.why-choose-us-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: rgb(255, 255, 255);
    clip-path: polygon(0% 64%, -451% -53%, -66% 65%, -16% 70%, 65%, 40% 60%, 70% 65%, 60% 70%, 70% 65%, 80% 60%, 90% 65%, 100% 70%, 100% 100%);
    transform: translateY(-40px);
    z-index: 1;
}



.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    margin-left: 60px;
    padding: 0 20px;
}

.why-choose-content {
    display: flex;
    align-items: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.why-choose-text {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
    opacity: 1;
}

.why-choose-title {
    font-size: 3.5rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 1.5rem;
    font-family: "Maven Pro", sans-serif;
    line-height: 1.2;
}

.why-choose-description {
    font-size: 1.4rem;
    line-height: 1.3;
    color: #525252;
    margin-bottom: 2rem;
    font-family: "Arial", sans-serif;
}

.why-choose-cta {
    margin-top: 2rem;
}
@keyframes bottomToTop {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-bottom-to-top {
  animation: bottomToTop 0.8s ease-out forwards;
}

.hidden-before-animate {
  opacity: 0;
  transform: translateY(50px);
}

.why-choose-image-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    justify-content: right;
    margin-top: 20px;
    margin-right: 10px;
    align-items: center;
}

.why-choose-image {
  width: 110%;
  height: 80%;
  object-fit: cover;
  object-position: center;
  margin-left: 450px;
  opacity: 1;
}
@keyframes zoomIn {
  0% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.zoom-in-on-scroll {
  animation: zoomIn 1s ease-out forwards;
}

.hidden-zoom {
  opacity: 0;
  transform: scale(1.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: transform, opacity;
}

.image-decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decorative-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    opacity: 0.8;
}

/* Responsive Design for Why Choose Us Section */
@media (max-width: 768px) {
    .why-choose-us-section {
        padding: 4rem 0;
    }

    .why-choose-container {
        padding: 0 2px; /* Minimal margins as per user preference */
    }

   .why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

    .why-choose-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .why-choose-text {
        max-width: 100%;
        margin-right: 35px;
    }

    .why-choose-image {
    width: 110%;
    height: 80%;
    object-fit: cover;
    object-position: center;
    margin-left: 15px;
}


    .why-choose-title {
        font-size: 2.50rem;
        align-items: center;
        margin-left: 4rem;
    }

    .circular-image-wrapper {
        width: 280px;
        height: 280px;
    }
}

@media (max-width: 480px) {
    .why-choose-title {
        font-size: 1.8rem;
    }

    .why-choose-description {
        font-size: 1.25rem;
    }


    .element-1 {
        width: 40px;
        height: 40px;
    }

    .element-2 {
        width: 30px;
        height: 30px;
    }

    .element-3 {
        width: 25px;
        height: 25px;
    }

    .element-4 {
        width: 20px;
        height: 20px;
    }
}



/* Why How Who Section Styles */
.why-how-who-section {
    padding: 4rem 0;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

.why-how-who-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Cards Row */
.cards-row {
    display: flex;
    gap: 2rem;
    margin-bottom: 4rem;
    justify-content: center;
    flex-wrap: wrap;
}

.info-card {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    position: relative;
    overflow: hidden;
}

/* Removed hover animations */

.card-icon {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    font-family: "Maven Pro", sans-serif;
}

.card-text {
    font-size: 1rem;
    line-height: 1.5;
    opacity: 0.95;
    font-family: "Arial", sans-serif;
}

/* Content Row */
.content-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.team-image-container {
    flex: 1.5;
    position: relative;
    min-width: 500px;
}

.team-image {
    width: 90%;
    object-fit: cover;
    margin-left: 30px;
    opacity: 1;
}
@keyframes zoomIn {
  0% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.zoom-in-on-scroll {
  animation: zoomIn 1s ease-out forwards;
}

.hidden-zoom {
  opacity: 0;
  transform: scale(1.1);
  transition: opacity 0.3s ease, transform 0.3s ease;
  will-change: transform, opacity;
}


.image-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decoration-shape {
    position: absolute;
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    border-radius: 50%;
    opacity: 0.8;
}

/* Removed float animation */

.who-content {
    flex: 1;
    min-width: 300px;
    margin-right: 200px;
    margin-left: 30px;
    opacity: 1;
}

.who-title {
    font-size: 3rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 0.5rem;
    font-family: "Maven Pro", sans-serif;
}

.who-text {
    font-size: 1.5rem;
    line-height: 1.3;
    font-weight: 300;
    color: #525252;
    margin-bottom: 2rem;
    font-family: "Arial", sans-serif;
}

.who-cta {
    margin-top: 2rem;
}
@keyframes slowBottomToTop {
  0% {
    opacity: 0;
    transform: translateY(60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slow-bottom-to-top {
  animation: slowBottomToTop 1.2s ease-out forwards;
}

.hidden-before-slow-animate {
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 0.4s ease, transform 0.4s ease;
  will-change: transform, opacity;
}


/* Responsive Design for Why How Who Section */
@media (max-width: 768px) {
    .why-how-who-section {
        padding: 3rem 0;
    }

    .why-how-who-container {
        padding: 0 2px; /* Minimal margins as per user preference */
    }

    .cards-row {
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .info-card {
        min-width: auto;
        max-width: none;
    }

    .content-row {
        flex-direction: column;
        gap: 2rem;
    }

    .team-image {
        height: 400px;
        width: 100vh;
        margin-left: 20px;
    }

    .who-title {
        font-size: 2rem;
        text-align: center;
    }

    .who-text {
        text-align: center;
    }

    .who-cta {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .info-card {
        padding: 1.5rem;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .who-title {
        font-size: 1.8rem;
    }

    .team-image {
        height: 350px;
        width: 98%;
        margin-left: 10px;
    }
}


/* Prevent horizontal scroll on all widths */
html, body, .hero-section, .hero-container, .hero-content, .hero-text {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
}

/* Additional responsive adjustments for content sections */
@media (max-width: 1023px) {
    .content-row {
        flex-direction: column;
    }
    
    .team-image-container {
        min-width: 100%;
    }
    
    .team-image {
        width: 100%;
        margin-left: 0;
    }
    
    .who-content {
        margin-right: 0;
        margin-left: 0;
        padding: 0 20px;
    }
}

@media (max-width: 767px) {
    .why-how-boxes {
        flex-direction: column;
    }
    
    .why-box, .how-box {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .box-content {
        padding: 15px;
    }
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Ensure containers are responsive */
.container, .why-choose-container, .why-how-who-container {
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
}

/* Ensure text remains readable on all devices */
p, h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

@media (max-width: 1024px) {
    /* .hero-title {
        margin-bottom: 1rem !important;
        margin-top: 15rem;
        font-size: 2.5rem;
    }  */
    .why-choose-container {
        margin-left: auto !important;
        margin-right: auto !important;
        padding-left: 20px !important;
        padding-right: 20px !important;
        width: 100% !important;
        box-sizing: border-box;
    }
    .why-choose-title {
        margin-left: 20px !important;
    }

    .why-choose-description{
        margin-left: 20px !important;  
    }
    .service-box {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}


@media (min-width: 769px) and (max-width: 1023px) {
    .why-choose-title {
        margin-top: 30px !important;
    }

    @media (min-width: 767px) and (max-width: 1024px) {
    .content-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        flex-wrap: nowrap;
    }
    .team-image-container {
        flex: 1 1 50%;
        max-width: 50%;
        min-width: 250px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
    .team-image {
        width: 90%;
        height: auto;
        border-radius: 50%;
        object-fit: cover;
        margin-left: 0;
    }
    .who-content {
        flex: 1 1 50%;
        max-width: 50%;
        min-width: 250px;
        padding: 0 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        text-align: left;
    }
}

@media (min-width: 1530px) and (max-width: 2060px) {
    .why-choose-us-section {
        margin-left: 60px !important;
        margin-right: 60px !important;
    }}
}

/* Button Responsive Styles */
@media (min-width: 1440px) {
    .btn {
        padding: 1.5rem 3rem;
        font-size: 1.2rem;
        min-width: 200px;
    }
}

@media (min-width: 1025px) and (max-width: 1439px) {
    .btn {
        padding: 1.3rem 2.5rem;
        font-size: 1.1rem;
        min-width: 180px;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .btn {
        padding: 1.2rem 2.2rem;
        font-size: 1rem;
        min-width: 160px;
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    .btn {
        padding: 1rem 2rem;
        font-size: 0.95rem;
        min-width: 140px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
        min-width: 120px;
    }
}
</style>

<!-- Animation JavaScript removed -->

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const heroSection = document.querySelector('.hero-section');
    const bg = document.querySelector('.hero-background');
    const content = document.querySelector('.hero-content');

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            bg.classList.add('animate-bg');
            content.classList.add('animate-content');
          }
        });
      },
      { threshold: 0.3 } // Trigger when 30% of section is visible
    );

    if (heroSection) {
      observer.observe(heroSection);
    }
  });

  document.addEventListener('DOMContentLoaded', function () {
  const boxes = document.querySelectorAll('.why-box, .how-box');

  const observer = new IntersectionObserver(
    (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          boxes.forEach((box, index) => {
            setTimeout(() => {
              box.classList.add('animate-slide-up');
            }, index * 200); // 200ms delay between items
          });
          observer.disconnect(); // Run once
        }
      });
    },
    { threshold: 0.3 }
  );

  // Observe the parent section or first box's parent
  if (boxes.length > 0) {
    const section = boxes[0].closest('section') || boxes[0].parentElement;
    observer.observe(section);
  }
});

  document.addEventListener("DOMContentLoaded", function () {
    const elements = [
      document.querySelector(".why-choose-title"),
      document.querySelector(".why-choose-description"),
      document.querySelector(".why-choose-cta")
    ];

    elements.forEach(el => {
      if (el) el.classList.add("hidden-before-animate");
    });

    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add("animate-bottom-to-top");
            entry.target.classList.remove("hidden-before-animate");
          }, index * 150); // Stagger animation
          obs.unobserve(entry.target);
        }
      });
    }, { threshold: 0.2 });

    elements.forEach(el => {
      if (el) observer.observe(el);
    });
  });

  document.addEventListener("DOMContentLoaded", function () {
    const image = document.querySelector(".why-choose-image");

    if (image) {
      image.classList.add("hidden-zoom");

      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            image.classList.add("zoom-in-on-scroll");
            image.classList.remove("hidden-zoom");
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.3 });

      observer.observe(image);
    }
  });


  document.addEventListener("DOMContentLoaded", function () {
    const teamImage = document.querySelector(".team-image");

    if (teamImage) {
      teamImage.classList.add("hidden-zoom");

      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            teamImage.classList.add("zoom-in-on-scroll");
            teamImage.classList.remove("hidden-zoom");
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.3 });

      observer.observe(teamImage);
    }
  });


  document.addEventListener("DOMContentLoaded", function () {
    const whoElements = [
      document.querySelector(".who-title"),
      document.querySelector(".who-text"),
      document.querySelector(".who-cta")
    ];

    whoElements.forEach(el => {
      if (el) el.classList.add("hidden-before-slow-animate");
    });

    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.classList.add("animate-slow-bottom-to-top");
            entry.target.classList.remove("hidden-before-slow-animate");
          }, index * 200); // Stagger effect
          obs.unobserve(entry.target);
        }
      });
    }, { threshold: 0.3 });

    whoElements.forEach(el => {
      if (el) observer.observe(el);
    });
  });

</script>

