<?php
/**
 * Template Name: Plain Language Services Page
 * Description: Custom Plain Language Services page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<section class="plainlang-hero">
  <div class="plainlang-hero-left">
    <div class="plainlang-hero-topline">Making clinical research crystal clear</div>
    <div class="plainlang-hero-headline">Plain language services</div>
    <div class="plainlang-hero-subheadline">There has never been more demand for effective plain language communication.</div>
    <div class="plainlang-hero-desc">
      Delivering breakthrough treatments involves many stakeholders – including researchers, regulators, grant approvers, healthcare professionals and patients. Everyone involved benefits from effective communication. However, the industry has only recently considered how best to communicate clinical research information more comprehensively to patients and the general public. This is recognised by global regulators and policymakers through regulatory requirements for plain language communication:
    </div>
    <div class="plainlang-hero-desc">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" style="width: 1.25em; height: 1.25em; vertical-align: middle; margin-right: 0.4em;" /> <a href="https://www.ecfr.gov/current/title-21/chapter-I/subchapter-A/part-50/subpart-B" target="_blank" style="color:#9263d9;">US FDA 21 CFR 50 Subpart B</a> and Article 28 & 29 of <a href="https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32014R0536" target="_blank" style="color:#9263d9;">European Union Clinical Trial Regulation (EU CTR) 536/2014</a> for the informed consent
      <br><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" style="width: 1.25em; height: 1.25em; vertical-align: middle; margin-right: 0.4em;" /> <a href="https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32014R0536" target="_blank" style="color:#9263d9;">EU CTR 536/2014</a> Article 37 and <a href="https://www.gov.uk/government/publications/clinical-trials-for-medicines-apply-for-authorisation-in-the-uk/summary-of-proposed-changes-to-the-uk-clinical-trials-regulations" target="_blank" style="color:#9263d9;">proposed changes to UK's Clinical Trial Legislation</a> for plain language summaries (layperson summaries)
      <br><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" style="width: 1.25em; height: 1.25em; vertical-align: middle; margin-right: 0.4em;" /> <a href="https://www.legislation.gov.uk/uksi/2012/1916/part/13/made" target="_blank" style="color:#9263d9;">Part 13, Chapter 1 of the UK's Human Medicines Regulations (HMRs)</a> and Article 54, Article 55 and Article 59 (Title V) of <a href="https://eur-lex.europa.eu/legal-content/EN/TXT/?uri=CELEX%3A32001L0083" target="_blank" style="color:#9263d9;">Directive 2001/83/EC</a> for patient information leaflet
    </div>
    <div class="plainlang-hero-desc">
      Translating technical terms and concepts used in clinical development into plain language requires significant expertise.
    </div>
  </div>
  <div class="plainlang-hero-right">
    <img class="plainlang-hero-image" src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Plain-Language-Services-Image.png" alt="Bridge the Gap: Simple to Complex" />
  </div>
</section>

<!-- Expertise Arch Section Start -->
<section class="plainlang-expertise-arch">
  <div class="plainlang-expertise-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="plainlang-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="plainlang-bg-corner-img" />
  </div>
  <div class="plainlang-expertise-content">
    <h2 class="plainlang-expertise-title">Effective plain language writing requires<br>expertise at many different levels</h2>
    <div class="plainlang-expertise-arch-img-wrap">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/plain-language-requires-significant-expertise-graphic (1).png" alt="Plain Language Expertise Arch" class="plainlang-expertise-arch-img" />
      <!-- Fullscreen Modal for the image -->
      <div class="expertise-arch-modal" tabindex="-1">
        <span class="expertise-arch-modal-close" tabindex="0">&times;</span>
        <img class="expertise-arch-modal-img" src="<?php echo get_template_directory_uri(); ?>/assets/images/plain-language-requires-significant-expertise-graphic (1).png" alt="Plain Language Expertise Arch Fullscreen" />
      </div>
    </div>
  </div>
</section>

<!-- Plain Language Services Features Section Start -->
<section class="plainlang-services-features">
  <div class="plainlang-services-features-inner">
    <h2 class="plainlang-services-features-title">We offer world-class plain language<br>services for clinical research by applying</h2>
    <div class="plainlang-services-features-grid">
      <div class="plainlang-feature-card">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Our-deep-understanding-Icon.svg" alt="Public Health Communication" class="plainlang-feature-icon" />
        <div class="plainlang-feature-text">Our deep understanding<br>of the complexity of<br>public health communication</div>
      </div>
      <div class="plainlang-feature-card">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/extensive-experience-Icon.svg" alt="Health Literacy" class="plainlang-feature-icon" />
        <div class="plainlang-feature-text">Our extensive experience<br>in health literacy and<br>numeracy principles</div>
      </div>
      <div class="plainlang-feature-card">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/setting.png" alt="Artificial Intelligence" class="plainlang-feature-icon" />
        <div class="plainlang-feature-text">Artificial intelligence,<br>sensitively applied for efficiently creating easy-to-understand, accurate,<br>and consistent outputs</div>
      </div>
      <div class="plainlang-feature-card">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Social-listening-Icon.svg" alt="Social Listening" class="plainlang-feature-icon" />
        <div class="plainlang-feature-text">Social listening to understand how patients<br>and public discuss health topics</div>
      </div>
      <div class="plainlang-feature-card">
        <img src="<?php echo get_template_directory_uri(); ?>//assets/images/Engaging-with-patient-Icon-90px.svg" alt="Patient Partners" class="plainlang-feature-icon" />
        <div class="plainlang-feature-text">Engaging with patient partners to better understand disease areas</div>
      </div>
    </div>
  </div>
</section>

<!-- Experience of Our Experts Section Start -->
<section class="plainlang-experts-experience">
  <h2 class="plainlang-experts-title">Experience of our experts</h2>
  <div class="plainlang-experts-grid">
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Completed hundreds of plain language communication materials</div>
    </div>
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Delivered both content and graphic design support</div>
    </div>
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Implemented plain language processes for study sponsors</div>
    </div>
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Trained clinical study teams on plain language concepts and processes</div>
    </div>
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Published articles on key developments in the plain language domain</div>
    </div>
    <div class="plainlang-expert-card">
      <div class="plainlang-expert-icon-wrap">
<svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>      </div>
      <div class="plainlang-expert-text">Contributed to the development of industry standards</div>
    </div>
  </div>
  <div class="plainlang-experts-btn-wrap">
    <a href="http://localhost/wordpress/insights/" class="plainlang-experts-btn">ACCESS OUR THOUGHT CAPITAL</a>
  </div>
</section>

<!-- Services Offered Section Start -->
<section class="plainlang-services-offered">
  <h2 class="plainlang-services-offered-title">Services offered</h2>
  <div class="plainlang-services-offered-main">
    <div class="plainlang-services-offered-bullet">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" />
      Plain language writing services, including but not limited to following documents
    </div>
    <div class="plainlang-services-docs-grid">
      <div class="plainlang-services-doc-card">Informed consent documents (ICDs)</div>
      <div class="plainlang-services-doc-card">Trial consent element documents (TCEDs)</div>
      <div class="plainlang-services-doc-card">Plain language protocol synopses</div>
      <div class="plainlang-services-doc-card">Plain language summaries (PLSs) for clinical trial results</div>
      <div class="plainlang-services-doc-card">Plain language summaries of publications</div>
      <div class="plainlang-services-doc-card">Patient information leaflets</div>
      <div class="plainlang-services-doc-card">Patient recruitment material</div>
      <div class="plainlang-services-doc-card">Public health education material</div>
    </div>
    <ul class="plainlang-services-list">
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Health literacy review of existing plain language material</li>
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Review of material by a patient representative panel (patient panel review) or public representatives (non-scientific panel review)</li>
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Facilitation of training for study teams on plain language concepts and processes</li>
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Graphic design support for plain language material (infographic, template designing, comic and video formats)</li>
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Advisory services, including understanding the implications of regulations and developing process documents, e.g., SOPs, templates, glossaries, lexicons, training materials</li>
      <li><img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Symbol-Bullet.svg" alt="Bullet" class="plainlang-services-bullet-icon" /> Translation and dissemination services</li>
    </ul>
  </div>
</section>

<!-- Get In Touch CTA Section Start -->
<section class="plainlang-get-in-touch-cta">
  <div class="plainlang-get-in-touch-bg">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Orange-1024x1024.png" alt="Decorative Orange Triangle" class="plainlang-cta-triangle plainlang-cta-triangle-left" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Blue-1024x1024.png" alt="Decorative Blue Triangle" class="plainlang-cta-triangle plainlang-cta-triangle-right" />
    <div class="plainlang-get-in-touch-content">
      <div class="plainlang-get-in-touch-heading">REQUEST ACCESS TO SAMPLES OF OUR PLAIN LANGUAGE VIDEO AND PDF SUMMARIES</div>
      <div class="plainlang-get-in-touch-title">Get In Touch</div>
      <a href="http://localhost/wordpress/contact-us/" class="plainlang-get-in-touch-btn">CONTACT US</a>
    </div>
  </div>
</section>

<style>
.plainlang-hero {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 48px 0 32px 0;
  min-height: 60vh;
}
.plainlang-hero-left {
  flex: 1 1 400px;
  padding: 0 40px;
  box-sizing: border-box;
}
.plainlang-hero-right {
  flex: 1 1 350px;
  min-width: 320px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 40px;
  opacity: 0;
  transform: scale(0.92);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-hero-right.zoom-in {
  opacity: 1;
  transform: scale(1);
}
.plainlang-hero-image {
  width: 100%;
  margin: 40px 0px 30px 30px;
  object-fit: cover;
}
.plainlang-hero-topline {
  color: #FF6A18;
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 18px;
  opacity: 0;
  transform: translateY(-60px);
  transition: opacity 1s cubic-bezier(0.23, 1, 0.32, 1), transform 1s cubic-bezier(0.23, 1, 0.32, 1);
}

.plainlang-hero-topline.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-hero-headline {
  color: #0072DA;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 12px;
  font-family: 'Maven Pro', sans-serif;
  line-height: 1.1;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1s cubic-bezier(0.23, 1, 0.32, 1), transform 1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-hero-headline.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-hero-subheadline {
  color: #ff9459;
  font-size: 1.50rem;
  font-weight: 600;
  margin-bottom: 24px;
  font-family: 'Maven Pro', sans-serif;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1s cubic-bezier(0.23, 1, 0.32, 1), transform 1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-hero-subheadline.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-hero-desc {
  color: #525252;
  font-size: 1.25rem;
  margin-bottom: 18px;
  font-family: Georgia, serif;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1s cubic-bezier(0.23, 1, 0.32, 1), transform 1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-hero-desc.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-hero-desc a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s;
}
.plainlang-hero-desc a:hover,
.plainlang-hero-desc a:focus {
  color: orange !important;
  text-decoration: none;
}
@media (max-width: 999px) {
  .plainlang-hero {
    flex-direction: column;
    padding: 32px 0 16px 0;
    display: flex;
  }

  .plainlang-hero-left {
    max-width: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .plainlang-hero-right {
    max-width: 100%;
    padding: 0 16px;
    justify-content: center;
    width: 100%;
  }

  /* Order for small screens (0-999px) */
  .plainlang-hero-topline {
    order: 1;
    font-size: 1.25rem;
    text-align: center;
  }

  .plainlang-hero-right {
    order: 2;
  }

  .plainlang-hero-headline {
    order: 3;
    font-size: 2.50rem;
  }

  .plainlang-hero-subheadline {
    order: 4;
    font-size: 1.3rem;
  }

  .plainlang-hero-desc {
    order: 5;
  }

  .plainlang-hero-image {
    max-width: 100%;
    border-radius: 14px;
    margin: 20px 0;
  }
}

@media (max-width: 600px) {
  .plainlang-hero {
    padding: 18px 0 8px 0;
  }

  .plainlang-hero-topline {
    font-size: 1.25rem;
    text-align: center;
  }

  .plainlang-hero-headline {
    font-size: 2.25rem;
    font-weight: 600;
  }

  .plainlang-hero-subheadline {
    font-size: 1.625rem;
    color: #ff9459;
  }

  .plainlang-hero-desc {
    font-size: 1.15rem;
  }
}
.plainlang-expertise-arch {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 520px;
}
.plainlang-expertise-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
}
.plainlang-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
  opacity: 0;
  transform: translateY(-60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-bg-top-img.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-bg-corner-img.slide-in {
  opacity: 1;
  transform: translateX(0);
}
.plainlang-expertise-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 80px;
}
.plainlang-expertise-title {
  color: #0072DA;
  font-size: 52px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32px;
  font-family: 'Maven Pro', sans-serif;
  line-height: 1.15;
  margin-top: 10rem;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-expertise-title.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-expertise-arch-img-wrap {
  position: relative;
  cursor: pointer;
}
.plainlang-expertise-arch-img-wrap::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* Reserved for future use */
}
.plainlang-expertise-arch-img-wrap::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  z-index: 2;
  will-change: opacity;
  transition: opacity 1s ease-out;
  pointer-events: none;
}
.plainlang-expertise-arch-img-wrap.overlay-active::after {
  opacity: 1;
  pointer-events: auto;
}
.plainlang-expertise-arch-img {
  width: 100%;
  max-width: 1000px;
  height: auto;
  display: block;
}
@media (max-width: 900px) {
  .plainlang-expertise-content {
    padding-top: 48px;
  }
  .plainlang-expertise-title {
    font-size: 2rem;
  }
  .plainlang-bg-corner-img {
    width: 120px;
    top: 16px;
    right: 4vw;
  }
}
@media (max-width: 600px) {
  .plainlang-expertise-content {
    padding-top: 24px;
  }
  .plainlang-expertise-title {
    font-size: 2rem;
    /* margin-bottom: 18px; */
  }
  .plainlang-expertise-arch-img-wrap {
    max-width: 100vw;
    padding: 0 4vw;
  }
  .plainlang-expertise-arch-img {
    max-width: 100vw;
  }
  .plainlang-bg-corner-img {
    width: 70px;
    top: 8px;
    right: 2vw;
  }
  .plainlang-expertise-bg-top {
    height: 120px;
  }
}
.plainlang-services-features {
  position: relative;
  background-color: #fff;
  padding: 64px 0 80px 0;
  width: 100%;
  min-height: 480px;
  background: url('<?php echo get_template_directory_uri(); ?>/assets/images/plain-language-section-bottom-bg.png') bottom center no-repeat;
  background-size: cover;
  z-index: 1;
}
.plainlang-services-features-inner {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.plainlang-services-features-title {
  color: #0072DA;
  font-size: 2.8rem;
  font-weight: 800;
  text-align: center;
  margin-bottom: 48px;
  font-family: 'Maven Pro', sans-serif;
  line-height: 1.15;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-services-features-title.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-services-features-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 32px;
  width: 100%;
  max-width: 1400px;
}
.plainlang-feature-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 6px 32px rgba(65,164,255,0.13);
  padding: 36px 18px 32px 18px;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  font-family: Georgia, serif;
  font-size: 1.35rem;
  color: #333;
  font-weight: 400;
  min-height: 320px;
  transition: box-shadow 0.2s, background 0.3s, color 0.3s;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1.5s cubic-bezier(0.23, 1, 0.32, 1), transform 1.5s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.2s, background 0.3s, color 0.3s;
}
.plainlang-feature-card.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-feature-card:hover {
  box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.18);
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #fff;
}
.plainlang-feature-card:hover .plainlang-feature-text {
  color: #fff;
}
.plainlang-feature-card:hover .plainlang-feature-icon {
  filter: brightness(0) invert(1);
  transition: filter 0.3s;
}
.plainlang-feature-icon {
  width: 68px;
  height: 68px;
  margin-bottom: 24px;
  display: block;
}
.plainlang-feature-text {
  font-size: 1.25rem;
  color: #333;
  font-family: Georgia, serif;
  line-height: 1.5;
}
@media (max-width: 1200px) {
  .plainlang-services-features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 32px 20px;
  }
}
@media (max-width: 800px) {
  .plainlang-services-features-grid {
    grid-template-columns: 1fr;
    gap: 28px 0;
  }
  .plainlang-feature-card {
    min-height: 220px;
    padding: 28px 10px 24px 10px;
  }
  .plainlang-services-features-title {
    font-size: 2rem;
  }
}
.plainlang-experts-experience {
  width: 100%;
  padding: 64px 0 32px 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.plainlang-experts-title {
  color: #0072DA;
  font-size: 2.6rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
  font-family: 'Maven Pro', sans-serif;
  opacity: 0;
  transform: translateY(-60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-experts-title.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-experts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 48px 36px;
  width: 100%;
  max-width: 1300px;
  margin-bottom: 48px;
}
.plainlang-expert-card {
    background: #fff;
    border-radius: 24px;
    border: 1px solid #eaeaea;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 36px 36px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: center;
    position: relative;
    height: 100%;
    box-sizing: border-box;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.5s cubic-bezier(0.23, 1, 0.32, 1), transform 1.5s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.2s;
}
.plainlang-expert-card.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.plainlang-expert-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);

  }
.plainlang-expert-icon-wrap {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(65,164,255,0.10);
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}
.plainlang-expert-icon {
  width: 48px;
  height: 48px;
  display: block;
}
.plainlang-expert-text {
  margin-top: 24px;
  font-size: 1.25rem;
  color: #333;
  font-family: Georgia, serif;
  line-height: 1.5;
}
.plainlang-experts-btn-wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1.5s cubic-bezier(0.23, 1, 0.32, 1), transform 1.5s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-experts-btn-wrap.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-experts-btn {
  background: linear-gradient(90deg, #ff9459 0%, #ff6a18 100%);
  color: #fff;
  font-size: 1.25rem;
  font-weight: 700;
  padding: 22px 48px;
  border-radius: 16px;
  text-decoration: none;
  box-shadow: 0 4px 24px rgba(255,106,24,0.10);
  margin-top: 24px;
  transition: background 0.2s, box-shadow 0.2s;
  letter-spacing: 1px;
}
.plainlang-experts-btn:hover {
  background: linear-gradient(90deg, #ff6a18 0%, #ff9459 100%);
  box-shadow: 0 6px 32px rgba(255,106,24,0.18);
}
@media (max-width: 1100px) {
  .plainlang-experts-grid {
    grid-template-columns: 1fr 1fr;
    gap: 36px 20px;
  }
}
@media (max-width: 700px) {
  .plainlang-experts-title {
    font-size: 1.5rem;
    margin-bottom: 24px;
  }
  .plainlang-experts-grid {
    grid-template-columns: 1fr;
    gap: 28px 0;
    padding: 0 30px;
  }
  .plainlang-expert-card {
    padding: 38px 10px 24px 10px;
    min-height: 120px;
  }
  .plainlang-expert-icon-wrap {
    width: 44px;
    height: 44px;
    top: -22px;
  }
  .plainlang-expert-icon {
    width: 32px;
    height: 32px;
  }
}
.plainlang-services-offered {
  width: 100%;
  padding: 64px 0 32px 0;
  background: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.plainlang-services-offered-title {
  color: #0072DA;
  font-size: 42px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 32px;
  font-family: 'Maven Pro', sans-serif;
  opacity: 0;
  transform: translateY(-60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-services-offered-title.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-services-offered-main {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  /* align-items: center;*/
} 
.plainlang-services-offered-bullet {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  color: #525252;
  font-family: Georgia, serif;
  margin-bottom: 32px;
  font-weight: 500;
}
.plainlang-services-bullet-icon {
  width: 1.5em;
  height: 1.5em;
  margin-right: 0.5em;
  vertical-align: middle;
}
.plainlang-services-docs-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 28px 32px;
  width: 100%;
  margin-bottom: 32px;
  opacity: 0;
  transform: translateY(60px);
  transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-services-docs-grid.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-services-doc-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(65,164,255,0.10);
  padding: 32px 18px;
  border: 1px solid #f0f0f0;
  text-align: center;
  font-family: Georgia, serif;
  font-size: 1.25rem;
  color: #525252;
  font-weight: 400;
  min-height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.plainlang-services-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  max-width: 1100px;
}
.plainlang-services-list li {
  display: flex;
  align-items: flex-start;
  font-size: 1.25rem;
  color: #525252;
  font-family: Georgia, serif;
  margin-bottom: 12px;
  font-weight: 500;
}
.plainlang-services-list li .plainlang-services-bullet-icon {
  margin-right: 0.6em;
  margin-top: 0.2em;
}
@media (max-width: 1100px) {
  .plainlang-services-docs-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px 16px;
  }
}
@media (max-width: 700px) {
  .plainlang-services-offered-title {
    font-size: 1.5rem;
    margin-bottom: 18px;
  }
  .plainlang-services-docs-grid {
    grid-template-columns: 1fr;
    gap: 16px 0;
    padding: 0 30px;
  }
  .plainlang-services-offered-main {
    padding: 0 8px;
  }
  .plainlang-services-doc-card {
    padding: 20px 8px;
    font-size: 1rem;
  }
  .plainlang-services-list {
    font-size: 1rem;
  }
}
.plainlang-get-in-touch-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.plainlang-get-in-touch-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
  opacity: 0;
  transform: translateY(-60px);
  transition: opacity 1.5s cubic-bezier(0.23, 1, 0.32, 1), transform 1.5s cubic-bezier(0.23, 1, 0.32, 1);
}
.plainlang-get-in-touch-bg.slide-in {
  opacity: 1;
  transform: translateY(0);
}
.plainlang-get-in-touch-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.plainlang-get-in-touch-heading {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 1px;
}
.plainlang-get-in-touch-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.plainlang-get-in-touch-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1), all 0.3s ease;
}
.plainlang-get-in-touch-btn.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.plainlang-get-in-touch-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.plainlang-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.plainlang-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.plainlang-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .plainlang-get-in-touch-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .plainlang-get-in-touch-title {
    font-size: 2.2rem;
  }
  .plainlang-get-in-touch-heading {
    font-size: 1.2rem;
  }
  .plainlang-cta-triangle {
    width: 100px;
  }
  .plainlang-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .plainlang-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .plainlang-get-in-touch-bg {
    min-height: 180px;
    padding: 0;
  }
  .plainlang-get-in-touch-content {
    padding: 24px 4vw 24px 4vw;
  }
  .plainlang-get-in-touch-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .plainlang-get-in-touch-heading {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .plainlang-get-in-touch-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .plainlang-cta-triangle {
    width: 48px;
  }
  .plainlang-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .plainlang-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}
.expertise-arch-modal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0; top: 0; width: 100vw; height: 100vh;
  background: rgba(0,0,0,0.92);
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}
.expertise-arch-modal.active {
  display: flex;
  animation: fadeIn 0.3s;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.expertise-arch-modal-img {
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 12px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.3);
  background: #fff;
}
.expertise-arch-modal-close {
  position: absolute;
  top: 32px;
  right: 48px;
  color: #fff;
  font-size: 3rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 10000;
  background: none;
  border: none;
  outline: none;
  transition: color 0.2s;
}
.expertise-arch-modal-close:hover,
.expertise-arch-modal-close:focus {
  color: #ff9459;
}
@media (max-width: 600px) {
  .expertise-arch-modal-img {
    max-width: 98vw;
    max-height: 80vh;
  }
  .expertise-arch-modal-close {
    top: 12px;
    right: 18px;
    font-size: 2.2rem;
  }
}
.plainlang-feature-card a,
.plainlang-feature-text a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s;
}
.plainlang-feature-card:hover a,
.plainlang-feature-text:hover a,
.plainlang-feature-card:hover .plainlang-feature-text a {
  color: #fff;
  text-decoration: none;
}
</style>
<script>
document.addEventListener('DOMContentLoaded', function() {
  var archImgWrap = document.querySelector('.plainlang-expertise-arch-img-wrap');
  var modal = archImgWrap ? archImgWrap.querySelector('.expertise-arch-modal') : null;
  var closeBtn = archImgWrap ? archImgWrap.querySelector('.expertise-arch-modal-close') : null;
  var img = archImgWrap ? archImgWrap.querySelector('.plainlang-expertise-arch-img') : null;

  if (archImgWrap && modal && closeBtn && img) {
    img.addEventListener('click', function(e) {
      modal.classList.add('active');
      document.body.style.overflow = 'hidden';
    });
    closeBtn.addEventListener('click', function() {
      modal.classList.remove('active');
      document.body.style.overflow = '';
    });
    modal.addEventListener('click', function(e) {
      if (e.target === modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && modal.classList.contains('active')) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
  }
});

function animateHeroTopline() {
  var el = document.querySelector('.plainlang-hero-topline');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}

window.addEventListener('scroll', animateHeroTopline);
document.addEventListener('DOMContentLoaded', animateHeroTopline);

function animateHeroHeadline() {
  var el = document.querySelector('.plainlang-hero-headline');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateHeroHeadline);
document.addEventListener('DOMContentLoaded', animateHeroHeadline);

function animateHeroSubheadline() {
  var el = document.querySelector('.plainlang-hero-subheadline');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateHeroSubheadline);
document.addEventListener('DOMContentLoaded', animateHeroSubheadline);

function animateHeroDesc() {
  var descs = document.querySelectorAll('.plainlang-hero-desc');
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  descs.forEach(function(el) {
    var rect = el.getBoundingClientRect();
    if (rect.top < windowHeight - 40) {
      el.classList.add('slide-in');
    } else {
      el.classList.remove('slide-in');
    }
  });
}
window.addEventListener('scroll', animateHeroDesc);
document.addEventListener('DOMContentLoaded', animateHeroDesc);

function animateHeroRight() {
  var el = document.querySelector('.plainlang-hero-right');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('zoom-in');
  } else {
    el.classList.remove('zoom-in');
  }
}
window.addEventListener('scroll', animateHeroRight);
document.addEventListener('DOMContentLoaded', animateHeroRight);

function animateBgTopImg() {
  var el = document.querySelector('.plainlang-bg-top-img');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateBgTopImg);
document.addEventListener('DOMContentLoaded', animateBgTopImg);

function animateBgCornerImg() {
  var el = document.querySelector('.plainlang-bg-corner-img');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateBgCornerImg);
document.addEventListener('DOMContentLoaded', animateBgCornerImg);

function animateExpertiseTitle() {
  var el = document.querySelector('.plainlang-expertise-title');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateExpertiseTitle);
document.addEventListener('DOMContentLoaded', animateExpertiseTitle);

function animateServicesFeaturesTitle() {
  var el = document.querySelector('.plainlang-services-features-title');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateServicesFeaturesTitle);
document.addEventListener('DOMContentLoaded', animateServicesFeaturesTitle);

function animateFeatureCards() {
  var cards = document.querySelectorAll('.plainlang-feature-card');
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  
  cards.forEach(function(card, index) {
    var rect = card.getBoundingClientRect();
    if (rect.top < windowHeight - 40) {
      // Add staggered delay based on index
      setTimeout(function() {
        card.classList.add('slide-in');
      }, index * 200); // 200ms delay between each card
    } else {
      card.classList.remove('slide-in');
    }
  });
}
window.addEventListener('scroll', animateFeatureCards);
document.addEventListener('DOMContentLoaded', animateFeatureCards);

function animateExpertsTitle() {
  var el = document.querySelector('.plainlang-experts-title');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateExpertsTitle);
document.addEventListener('DOMContentLoaded', animateExpertsTitle);

function animateExpertCards() {
  var cards = document.querySelectorAll('.plainlang-expert-card');
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  
  cards.forEach(function(card, index) {
    var rect = card.getBoundingClientRect();
    if (rect.top < windowHeight - 40) {
      // Add staggered delay based on index
      setTimeout(function() {
        card.classList.add('slide-in');
      }, index * 250); // 250ms delay between each card
    } else {
      card.classList.remove('slide-in');
    }
  });
}
window.addEventListener('scroll', animateExpertCards);
document.addEventListener('DOMContentLoaded', animateExpertCards);

function animateExpertsBtnWrap() {
  var el = document.querySelector('.plainlang-experts-btn-wrap');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateExpertsBtnWrap);
document.addEventListener('DOMContentLoaded', animateExpertsBtnWrap);

function animateServicesOfferedTitle() {
  var el = document.querySelector('.plainlang-services-offered-title');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateServicesOfferedTitle);
document.addEventListener('DOMContentLoaded', animateServicesOfferedTitle);

function animateServicesDocsGrid() {
  var el = document.querySelector('.plainlang-services-docs-grid');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateServicesDocsGrid);
document.addEventListener('DOMContentLoaded', animateServicesDocsGrid);

function animateGetInTouchBg() {
  var el = document.querySelector('.plainlang-get-in-touch-bg');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateGetInTouchBg);
document.addEventListener('DOMContentLoaded', animateGetInTouchBg);

function animateGetInTouchBtn() {
  var el = document.querySelector('.plainlang-get-in-touch-btn');
  if (!el) return;
  var rect = el.getBoundingClientRect();
  var windowHeight = window.innerHeight || document.documentElement.clientHeight;
  if (rect.top < windowHeight - 40) {
    el.classList.add('slide-in');
  } else {
    el.classList.remove('slide-in');
  }
}
window.addEventListener('scroll', animateGetInTouchBtn);
document.addEventListener('DOMContentLoaded', animateGetInTouchBtn);

// Function to reorder plainlang-hero elements for small screens
var originalStructureStored = false;
var originalHeroHTML = '';

function reorderPlainlangHero() {
  var heroSection = document.querySelector('.plainlang-hero');
  var heroLeft = document.querySelector('.plainlang-hero-left');
  var heroRight = document.querySelector('.plainlang-hero-right');
  var topline = document.querySelector('.plainlang-hero-topline');
  var headline = document.querySelector('.plainlang-hero-headline');
  var subheadline = document.querySelector('.plainlang-hero-subheadline');
  var descs = document.querySelectorAll('.plainlang-hero-desc');

  if (!heroSection) return;

  // Store original structure on first run
  if (!originalStructureStored && heroLeft && heroRight) {
    originalHeroHTML = heroSection.innerHTML;
    originalStructureStored = true;
  }

  if (window.innerWidth <= 999) {
    // Only reorder if we have the original structure
    if (heroLeft && heroRight && topline && headline && subheadline) {
      // Clear the hero section
      heroSection.innerHTML = '';

      // Create a container for all elements
      var container = document.createElement('div');
      container.className = 'plainlang-hero-container';
      container.style.cssText = 'display: flex; flex-direction: column; width: 100%; padding: 0 16px;';

      // Add elements in the desired order
      container.appendChild(topline);
      container.appendChild(heroRight);
      container.appendChild(headline);
      container.appendChild(subheadline);

      // Add all description elements
      descs.forEach(function(desc) {
        container.appendChild(desc);
      });

      heroSection.appendChild(container);
    }
  } else {
    // For larger screens: restore original structure
    if (originalStructureStored && !heroSection.querySelector('.plainlang-hero-left')) {
      heroSection.innerHTML = originalHeroHTML;
    }
  }
}

// Run on load and resize
document.addEventListener('DOMContentLoaded', reorderPlainlangHero);
window.addEventListener('resize', reorderPlainlangHero);
</script>

<?php get_footer(); ?> 