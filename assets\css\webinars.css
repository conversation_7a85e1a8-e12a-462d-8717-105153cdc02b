.webinars-page-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 80px 20px;
}

.webinars-page-container {
    margin: 0 auto;
    text-align: center;
}

.webinars-page-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 56px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 60px 0;
    line-height: 1.2;
}

.webinars-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40px;
    margin-top: 40px;
    /* max-width: 1200px; */
    margin-left: auto;
    margin-right: auto;
}

.webinar-card {
    background: white;
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    transition: all 0.3s ease;
    text-align: left;
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.webinar-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 32px;
    font-weight: 500;
    color: #0072DA;
    margin: 0 0 20px 0;
    line-height: 1.3;
}

.webinar-speaker {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 18px 0;
}

.webinar-date {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 18px 0;
}

.webinar-time {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    margin: 0 0 28px 0;
}

.webinar-link {
    font-family: "Maven Pro", sans-serif;
    font-size: 22px;
    font-weight: 500;
    color: #ff7f3f;
    text-decoration: none;
    margin-top: 20px;
    transition: color 0.2s;
    text-align: center;
    display: block;
}

.webinar-link:hover {
    color: #ff6b2b;
    text-decoration: underline;
}

.conferences-cta-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 4rem 1rem;
    background: #f8f9fa;
    overflow: hidden;
    margin-top: 80px;
}

.conferences-cta-container {
    /* max-width: 1200px; */
    margin: 0 auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.conferences-cta-background {
    max-width: 800px;
    width: 100%;
    padding: 4rem 3rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #4A90E2 0%, #0072DA 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    box-shadow: 0 10px 40px rgba(0, 114, 218, 0.2);
}

.conferences-cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 600px;
    margin: 0 auto;
}

.conferences-cta-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    opacity: 0.9;
    font-family: "Maven Pro", sans-serif;
    text-transform: uppercase;
}

.conferences-cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    font-family: "Maven Pro", sans-serif;
    color: white;
}

.conferences-cta-button {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

.conferences-cta-button:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

.cta-shape-left {
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.8;
}

.cta-shape-left img {
    width: 200px;
    height: auto;
    display: block;
}

.cta-shape-right {
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.7;
}

.cta-shape-right img {
    width: 250px;
    height: auto;
    display: block;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .webinars-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    .webinar-card {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .webinars-page-wrapper {
        padding: 60px 10px;
    }
    .webinars-page-title {
        font-size: 36px;
    }
    .webinar-title {
        font-size: 22px;
    }
    .webinar-speaker, .webinar-date, .webinar-time {
        font-size: 16px;
    }
    .webinar-link {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .webinar-card {
        padding: 18px 10px;
    }
    .conferences-cta-background {
        padding: 2.5rem 1rem;
    }
    .conferences-cta-title {
        font-size: 2rem;
    }
    .conferences-cta-subtitle {
        font-size: 0.8rem;
    }
    .conferences-cta-button {
        padding: 0.75rem 1.5rem;
    }
    .cta-shape-left, .cta-shape-right {
        display: none;
    }
} 